# Production Environment Variables for Coolify
# gAds Supercharge Application

# Application Configuration
NODE_ENV=production
PORT=3001

# Domain Configuration
FRONTEND_URL=https://gads-supercharge.online
BACKEND_URL=https://api.gads-supercharge.online
MAIL_URL=https://mail.gads-supercharge.online

# Database Configuration
POSTGRES_DB=gads_db
POSTGRES_USER=gads_user
POSTGRES_PASSWORD=YOUR_SECURE_POSTGRES_PASSWORD_HERE
DB_HOST=postgres
DB_PORT=5432
DB_NAME=gads_db
DB_USER=gads_user
DB_PASSWORD=YOUR_SECURE_POSTGRES_PASSWORD_HERE

# Redis Configuration
REDIS_HOST=redis
REDIS_PORT=6379

# Security Configuration
JWT_SECRET=YOUR_SECURE_JWT_SECRET_HERE_32_CHARS_MIN
CORS_ORIGIN=https://gads-supercharge.online

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# Session Configuration
SESSION_TIMEOUT_HOURS=24
CLEANUP_EXPIRED_SESSIONS_HOURS=1

# Email Configuration
EMAIL_FROM=<EMAIL>
ADMIN_EMAIL=<EMAIL>

# Development/Staging Email (MailHog)
SMTP_HOST=mailhog
SMTP_PORT=1025

# Production Email (SMTP Relay)
SMTP_RELAY_HOST=smtp.sendgrid.net
SMTP_USERNAME=apikey
SMTP_PASSWORD=YOUR_SENDGRID_API_KEY_HERE

# Build Configuration
VITE_API_URL=https://api.gads-supercharge.online/api
VITE_NODE_ENV=production
BUILDPLATFORM=linux/arm64

# Node.js ARM64 Optimizations
NODE_OPTIONS=--max-old-space-size=400
UV_THREADPOOL_SIZE=4

# SSL/TLS Configuration
SSL_CERT_PATH=/etc/ssl/certs
SSL_KEY_PATH=/etc/ssl/private

# Logging Configuration
LOG_LEVEL=info
LOG_FORMAT=json

# Monitoring Configuration
HEALTH_CHECK_INTERVAL=30s
HEALTH_CHECK_TIMEOUT=10s
HEALTH_CHECK_RETRIES=3

# Coolify Specific Environment Variables
COOLIFY_MANAGED=true
COOLIFY_SSL=letsencrypt
COOLIFY_HTTPS=true

# DigitalOcean ARM64 Specific
PLATFORM=linux/arm64
ARCH=arm64

# Backup Configuration
BACKUP_SCHEDULE=0 2 * * *
BACKUP_RETENTION=30d

# Performance Tuning
MAX_MEMORY_POSTGRES=1G
MAX_MEMORY_REDIS=256M
MAX_MEMORY_BACKEND=512M
MAX_MEMORY_FRONTEND=128M

# Google Ads API Configuration (if needed)
GOOGLE_ADS_CLIENT_ID=YOUR_GOOGLE_ADS_CLIENT_ID
GOOGLE_ADS_CLIENT_SECRET=YOUR_GOOGLE_ADS_CLIENT_SECRET
GOOGLE_ADS_DEVELOPER_TOKEN=YOUR_GOOGLE_ADS_DEVELOPER_TOKEN
GOOGLE_ADS_CUSTOMER_ID=YOUR_GOOGLE_ADS_CUSTOMER_ID

# Analytics Configuration
ANALYTICS_ENABLED=true
ANALYTICS_TRACKING_ID=YOUR_ANALYTICS_ID

# Feature Flags
FEATURE_ADMIN_PANEL=true
FEATURE_DARK_MODE=true
FEATURE_NOTIFICATIONS=true
FEATURE_CACHING=true
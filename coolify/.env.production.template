# gAds Supercharge - Production Environment Configuration
# Copy this file to .env.production and update with your actual values

# Domain Configuration
FRONTEND_URL=https://gads-supercharge.online
BACKEND_URL=https://api.gads-supercharge.online

# Database Configuration
POSTGRES_DB=gads_db
POSTGRES_USER=gads_user
POSTGRES_PASSWORD=GENERATE_SECURE_PASSWORD_32_CHARS

# JWT Configuration
JWT_SECRET=GENERATE_SUPER_SECURE_JWT_SECRET_MINIMUM_64_CHARS

# Email Configuration
EMAIL_FROM=<EMAIL>
ADMIN_EMAIL=<EMAIL>

# SMTP Configuration (for production email)
SMTP_RELAY_HOST=smtp.mailgun.com
SMTP_USERNAME=your-mailgun-username
SMTP_PASSWORD=your-mailgun-password

# Security Configuration
CORS_ORIGIN=https://gads-supercharge.online
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# Session Configuration
SESSION_TIMEOUT_HOURS=24
CLEANUP_EXPIRED_SESSIONS_HOURS=1

# Node.js Configuration
NODE_ENV=production
# gAds Supercharge

A comprehensive Google Ads automation and management platform with enterprise-grade security, multilingual support, and production-ready deployment configurations.

## 🚀 Quick Start

### Full-Stack Development (Recommended)
```bash
# Clone repository
git clone <repository-url>
cd gads-supercharge

# Start all services (PostgreSQL + Backend + Frontend + Email)
docker-compose up -d

# Access application
open http://localhost:5173
```

### Production Deployment (Coolify v4)
```bash
# See complete deployment guide
./coolify/README.md

# Domain: gads-supercharge.online
# Platform: DigitalOcean VPS with Coolify v4.0.0-beta.420.5
```

### Access Points
- **Frontend**: http://localhost:5173
- **Backend API**: http://localhost:3001/api
- **Database**: PostgreSQL localhost:5432
- **Email UI**: http://localhost:8025 (MailHog)

## ✨ Full-Stack Features

### Core Functionality
- **Script Generators**: Automated Google Ads script generation with obfuscated variables
- **Telegram Integration**: Performance reports to Telegram channels
- **Airtable Integration**: P&L reporting automation
- **Budget Management**: Automated budget updates with notifications
- **Performance Analytics**: Campaign and keyword performance tracking

### Advanced Features
- **Enterprise Security**: SQL injection prevention, XSS protection, input validation
- **User Authentication**: JWT-based secure login with bcryptjs password hashing
- **Multilingual Support**: 509+ translation keys with database-driven English/Ukrainian content
- **Activity Tracking**: Comprehensive user activity logging and analytics
- **Real-time Language Switching**: Seamless language changes without page reload
- **Production Ready**: Complete Coolify v4 deployment configuration
- **Role-based Access**: Admin and user roles with different permissions
- **Session Management**: Device tracking and session analytics

### Security & Performance
- **Script Security**: Input escaping, JSON sanitization, and secure token handling
- **SQL Injection Prevention**: Parameterized queries with field whitelisting
- **Variable Obfuscation**: All generated scripts use randomized variable names  
- **Rate Limiting**: DDoS protection and API throttling
- **CORS Protection**: Secure cross-origin resource sharing
- **Health Monitoring**: Service health checks and automated monitoring
- **Docker Containerization**: Multi-service production deployment

## 🔐 Login

- **URL**: http://localhost:5173/login
- **Email**: `<EMAIL>`
- **Password**: `Admin2025!Secure#`
- **Role**: Admin (full access to all tools and settings)

## 📚 Documentation

- **[Full Documentation](docs/README.md)** - Complete project documentation
- **[Project Context](docs/context.md)** - Architecture and components
- **[Deployment Guide](docs/deployment-context.md)** - Deployment instructions
- **[Optimization Plan](docs/netlify-optimization-plan.md)** - Performance optimization

## 🛠️ Available Tools

- Google Ads Budget Updater
- Telegram Script Generator
- AirTable Script Generator
- Campaign Performance Analysis
- Keyword Performance Tracking
- Device Bid Optimization
- And many more...

## 🌐 Live Demo

**Production**: https://gads-supercharge.online (Full-stack with database)
**Legacy**: https://gads-supercharge.netlify.app/ (Frontend-only, deprecated)
**Development**: http://localhost:5173 (Full-stack with database)

## 🆕 Recent Updates (2025-07-16)

### ✅ Critical Bug Fixes v1.40-activity-fix
- **TRACKING CODE FIX**: Fixed 500 error in admin tracking code save functionality
- **Database Schema**: Corrected `action` to `activity_type` in all admin activity logging
- **Backend v1.37-activity-fix**: Fixed admin routes in backend/src/routes/admin.js
- **SEO Preview**: Fixed preview to show actual database values instead of translation keys
- **MailHog Security**: Complex subdomain (mail79fc9f234318788645214cfb9d56a572.gads-supercharge.online)

### ✅ Production Deployment Status
- **Live Site**: https://gads-supercharge.online (fully functional)
- **Backend API**: https://api.gads-supercharge.online/health (operational)
- **Email Testing**: https://mail79fc9f234318788645214cfb9d56a572.gads-supercharge.online:8025
- **Admin Dashboard**: <NAME_EMAIL> login
- **Docker Images**: Multi-platform (AMD64 + ARM64) production-ready

### ✅ Security & Performance
- **Complete Security Audit**: All 12 script generators tested and secured
- **User Flow Optimization**: Fixed Preview/Generate button conflicts across all tools
- **Unified Navigation**: Integrated "Back to Generator" buttons in all tools
- **Enhanced Translations**: 509+ translation keys (expanded from 487)
- **Database-driven Content**: Real-time language switching without page reload

## 📄 License

MIT License - see [LICENSE](LICENSE) for details.

import React, { useState } from 'react';
import { useForm } from 'react-hook-form';
import { MapPin } from 'lucide-react';
import Header from '../components/Header';
import { useLanguage } from '../contexts/LanguageContext';
import SimpleCaptcha from '../components/Turnstile';

type FormData = {
  name: string;
  email: string;
  subject: string;
  message: string;
};

const ContactPage: React.FC = () => {
  const { t } = useLanguage();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitStatus, setSubmitStatus] = useState<{ success: boolean; message: string } | null>(null);
  const [turnstileToken, setTurnstileToken] = useState<string>('');

  const { register, handleSubmit, reset, formState: { errors } } = useForm<FormData>();

  const onSubmit = async (data: FormData) => {
    setIsSubmitting(true);
    setSubmitStatus(null);
    
    try {
      // You would typically call your backend API here
      // This is a placeholder for the actual API call
      const response = await fetch('/api/contact', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          to: '<EMAIL>',
          from: data.email,
          subject: `Contact Form: ${data.subject}`,
          text: `Name: ${data.name}\nEmail: ${data.email}\n\n${data.message}`,
        }),
      });

      if (response.ok) {
        setSubmitStatus({
          success: true,
          message: t('contact.form.success_message')
        });
        reset();
      } else {
        throw new Error('Failed to send message');
      }
    } catch (error) {
      console.error('Error submitting form:', error);
      setSubmitStatus({
        success: false,
        message: t('contact.form.error_message')
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="flex flex-col min-h-screen">
      <Header />
      <main className="flex-grow bg-gradient-to-b from-gray-900 to-gray-800 text-white overflow-hidden">
        <div className="relative pt-28 pb-20 md:pt-36 md:pb-28">
          {/* Background elements */}
          <div className="absolute inset-0 overflow-hidden">
            <div className="absolute inset-0 bg-[radial-gradient(circle_at_center,_var(--tw-gradient-stops))] from-blue-900/20 to-transparent"></div>
            <div className="absolute inset-0 bg-grid-white/[0.05] [mask-image:radial-gradient(ellipse_at_center,transparent_20%,black)]"></div>
          </div>
          
          <div className="container mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
            <div className="max-w-7xl mx-auto">
              {/* Header */}
              <div className="text-center mb-16">
                <h1 className="text-4xl sm:text-5xl md:text-6xl font-bold tracking-tight mb-6">
                  <span className="text-transparent bg-clip-text bg-gradient-to-r from-blue-400 to-cyan-400">{t('contact.page.title')}</span>
                </h1>
                <p className="text-lg md:text-xl text-blue-100/90 max-w-3xl mx-auto">
                  {t('contact.page.subtitle')}
                </p>
              </div>

              <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
                {/* Contact Form */}
                <div className="bg-gray-800/50 backdrop-blur-sm rounded-2xl p-6 md:p-8 border border-gray-700/50">
                  {submitStatus ? (
                    <div className={`p-4 rounded-lg ${submitStatus.success ? 'bg-green-900/30 border border-green-800' : 'bg-red-900/30 border border-red-800'}`}>
                      <p className={submitStatus.success ? 'text-green-300' : 'text-red-300'}>{submitStatus.message}</p>
                      {submitStatus.success && (
                        <button
                          onClick={() => setSubmitStatus(null)}
                          className="mt-4 text-sm text-blue-400 hover:text-blue-300 transition-colors"
                        >
                          {t('contact.form.send_another')}
                        </button>
                      )}
                    </div>
                  ) : (
                    <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
                      <div>
                        <label htmlFor="name" className="block text-sm font-medium text-gray-300 mb-2">
                          {t('contact.form.name')} <span className="text-red-400">*</span>
                        </label>
                        <input
                          type="text"
                          id="name"
                          {...register('name', { required: t('contact.form.name_required') })}
                          className={`w-full px-4 py-3 rounded-lg bg-gray-700/50 border ${errors.name ? 'border-red-500' : 'border-gray-600'} text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent`}
                          placeholder={t('contact.form.name_placeholder')}
                        />
                        {errors.name && (
                          <p className="mt-1 text-sm text-red-400">{errors.name.message}</p>
                        )}
                      </div>

                      <div>
                        <label htmlFor="email" className="block text-sm font-medium text-gray-300 mb-2">
                          {t('contact.form.email')} <span className="text-red-400">*</span>
                        </label>
                        <input
                          type="email"
                          id="email"
                          {...register('email', {
                            required: t('contact.form.email_required'),
                            pattern: {
                              value: /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i,
                              message: t('contact.form.email_invalid')
                            }
                          })}
                          className={`w-full px-4 py-3 rounded-lg bg-gray-700/50 border ${errors.email ? 'border-red-500' : 'border-gray-600'} text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent`}
                          placeholder={t('contact.form.email_placeholder')}
                        />
                        {errors.email && (
                          <p className="mt-1 text-sm text-red-400">{errors.email.message}</p>
                        )}
                      </div>

                      <div>
                        <label htmlFor="subject" className="block text-sm font-medium text-gray-300 mb-2">
                          {t('contact.form.subject')} <span className="text-red-400">*</span>
                        </label>
                        <input
                          type="text"
                          id="subject"
                          {...register('subject', { required: t('contact.form.subject_required') })}
                          className={`w-full px-4 py-3 rounded-lg bg-gray-700/50 border ${errors.subject ? 'border-red-500' : 'border-gray-600'} text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent`}
                          placeholder={t('contact.form.subject_placeholder')}
                        />
                        {errors.subject && (
                          <p className="mt-1 text-sm text-red-400">{errors.subject.message}</p>
                        )}
                      </div>

                      <div>
                        <label htmlFor="message" className="block text-sm font-medium text-gray-300 mb-2">
                          {t('contact.form.message')} <span className="text-red-400">*</span>
                        </label>
                        <textarea
                          id="message"
                          rows={5}
                          {...register('message', { required: t('contact.form.message_required') })}
                          className={`w-full px-4 py-3 rounded-lg bg-gray-700/50 border ${errors.message ? 'border-red-500' : 'border-gray-600'} text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent`}
                          placeholder={t('contact.form.message_placeholder')}
                        ></textarea>
                        {errors.message && (
                          <p className="mt-1 text-sm text-red-400">{errors.message.message}</p>
                        )}
                      </div>

                      {/* Captcha */}
                      <div>
                        <label className="block text-sm font-medium text-gray-300 mb-2">
                          🔒 Security Verification
                        </label>
                        <SimpleCaptcha
                          onVerify={(isValid) => setTurnstileToken(isValid ? 'verified' : '')}
                          className="mb-4"
                        />
                      </div>

                      <div>
                        <button
                          type="submit"
                          disabled={isSubmitting || !turnstileToken}
                          className="w-full py-3 px-6 bg-blue-600 hover:bg-blue-700 disabled:bg-gray-600 disabled:cursor-not-allowed text-white font-medium rounded-lg transition-colors duration-200 flex items-center justify-center"
                        >
                          {isSubmitting ? (
                            <>
                              <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                              </svg>
                              {t('contact.form.sending')}
                            </>
                          ) : t('contact.form.send_button')}
                        </button>
                      </div>
                    </form>
                  )}
                </div>

                {/* Contact Information */}
                <div className="space-y-8">
                  <div>
                    <h2 className="text-2xl font-bold text-white mb-6">{t('contact.info.title')}</h2>
                    <p className="text-gray-300 mb-8">
                      {t('contact.info.description')}
                    </p>
                  </div>

                  <div className="space-y-6">
                    <div className="flex items-start">
                      <div className="flex-shrink-0 bg-blue-500/10 p-3 rounded-lg">
                        <MapPin className="h-6 w-6 text-blue-400" />
                      </div>
                      <div className="ml-4">
                        <h3 className="text-lg font-medium text-white">{t('contact.info.location.title')}</h3>
                        <p className="text-gray-300">{t('contact.info.location.remote')}</p>
                        <p className="text-gray-400 text-sm mt-1">{t('contact.info.location.worldwide')}</p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </main>
    </div>
  );
};

export default ContactPage;

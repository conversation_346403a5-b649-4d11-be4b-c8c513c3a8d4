import React, { useState } from 'react';
import { useForm } from 'react-hook-form';
import { useLanguage } from '../contexts/LanguageContext';
import SimpleCaptcha from '../components/Turnstile';

type FormData = {
  name: string;
  email: string;
  subject: string;
  message: string;
};

const PortfolioPage: React.FC = () => {
  const { t } = useLanguage();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitStatus, setSubmitStatus] = useState<{ success: boolean; message: string } | null>(null);
  const [captchaVerified, setCaptchaVerified] = useState(false);

  const { register, handleSubmit, reset, formState: { errors } } = useForm<FormData>();

  const onSubmit = async (data: FormData) => {
    setIsSubmitting(true);
    setSubmitStatus(null);

    try {
      // Placeholder for actual API call
      const response = await fetch('/api/contact', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          to: '<EMAIL>', // Replace with your actual recipient
          from: data.email,
          subject: `Portfolio Page Inquiry: ${data.subject}`,
          text: `Name: ${data.name}\nEmail: ${data.email}\n\n${data.message}`,
        }),
      });

      if (response.ok) {
        setSubmitStatus({
          success: true,
          message: 'Your message has been sent successfully! We\'ll get back to you soon.'
        });
        reset();
      } else {
        throw new Error('Failed to send message');
      }
    } catch (error) {
      console.error('Error submitting form:', error);
      setSubmitStatus({
        success: false,
        message: 'There was an error sending your message. Please try again later.'
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-b from-gray-900 to-gray-800 text-white overflow-hidden">
      <div className="relative pt-28 pb-20 md:pt-36 md:pb-28">
          {/* Background elements - similar to ContactPage */}
          <div className="absolute inset-0 overflow-hidden">
            <div className="absolute inset-0 bg-[radial-gradient(circle_at_center,_var(--tw-gradient-stops))] from-blue-900/20 to-transparent"></div>
            <div className="absolute inset-0 bg-grid-white/[0.05] [mask-image:radial-gradient(ellipse_at_center,transparent_20%,black)]"></div>
          </div>

        <div className="container mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
          <div className="max-w-7xl mx-auto">
              {/* Portfolio Header */}
              <div className="text-center mb-12 md:mb-16">
                <h1 className="text-4xl sm:text-5xl md:text-6xl font-bold tracking-tight mb-6">
                  <span className="text-transparent bg-clip-text bg-gradient-to-r from-blue-400 to-cyan-400">
                    {t('portfolio.header.title') || 'Our Portfolio & Expertise'}
                  </span>
                </h1>
                <p className="text-lg md:text-xl text-blue-100/90 max-w-3xl mx-auto">
                  {t('portfolio.header.subtitle') || 'We are professionals with more than 10 years of experience and have certification in almost all products of Google advertising, Google marketing platform, Google developers.'}
                </p>
              </div>

              {/* Content & Form Section */}
              <div className="flex flex-col lg:flex-row gap-12">
                {/* Left Column: Text & Call to Action */}
                <div className="lg:flex-1 lg:pr-8 flex flex-col">
                  <h2 className="text-2xl sm:text-3xl font-semibold text-white mb-6">
                    {t('portfolio.why_choose.title') || 'Why Choose Us?'}
                  </h2>
                  <div className="space-y-4 text-blue-100/80 mb-8">
                    <p>
                      {t('portfolio.why_choose.intro') || 'With over a decade of dedicated experience, our team brings unparalleled expertise to the table. We are certified across a comprehensive suite of Google\'s advertising and marketing tools, including:'}
                    </p>
                    <ul className="list-disc list-inside space-y-2 pl-4">
                      <li>{t('portfolio.why_choose.google_ads') || 'Google Ads (Search, Display, Video, Shopping, App)'}</li>
                      <li>{t('portfolio.why_choose.marketing_platform') || 'Google Marketing Platform (Display & Video 360, Search Ads 360, Analytics 360)'}</li>
                      <li>{t('portfolio.why_choose.developer_products') || 'Google Developer products relevant to marketing and analytics integrations.'}</li>
                    </ul>
                    <p>
                      {t('portfolio.why_choose.conclusion') || 'Our deep understanding of these platforms allows us to craft highly effective strategies tailored to your unique business goals, ensuring maximum ROI and sustainable growth.'}
                    </p>
                  </div>
                   <p className="text-lg md:text-xl text-blue-100/90 mb-6">
                    {t('portfolio.why_choose.cta_text') || 'If you have any questions or wish to discuss your project, please fill out the application form and we will contact you.'}
                  </p>
                  {/* Certification Badges */}
                  <div className="mt-8">
                    <h3 className="text-xl font-semibold text-white mb-4">{t('portfolio.certifications.title') || 'Our Certifications'}</h3>
                    <div className="grid grid-cols-3 gap-4">
                      <div className="bg-gray-800/50 p-4 rounded-lg border border-gray-700/50 flex flex-col items-center">
                        <div className="w-16 h-16 bg-blue-500/20 rounded-full flex items-center justify-center mb-2">
                          <svg className="w-8 h-8 text-blue-400" fill="currentColor" viewBox="0 0 24 24">
                            <path d="M12 2L4 5v6.09c0 5.05 3.41 9.76 8 10.91 4.59-1.15 8-5.86 8-10.91V5l-8-3zm-1.06 13.54L7.4 12l1.41-1.41 2.12 2.12 4.24-4.24 1.41 1.41-5.64 5.66z"/>
                          </svg>
                        </div>
                        <span className="text-sm text-blue-100/80 text-center">{t('portfolio.certifications.google_ads') || 'Google Ads Certified'}</span>
                      </div>
                      <div className="bg-gray-800/50 p-4 rounded-lg border border-gray-700/50 flex flex-col items-center">
                        <div className="w-16 h-16 bg-green-500/20 rounded-full flex items-center justify-center mb-2">
                          <svg className="w-8 h-8 text-green-400" fill="currentColor" viewBox="0 0 24 24">
                            <path d="M12 2L4 5v6.09c0 5.05 3.41 9.76 8 10.91 4.59-1.15 8-5.86 8-10.91V5l-8-3zm-1.06 13.54L7.4 12l1.41-1.41 2.12 2.12 4.24-4.24 1.41 1.41-5.64 5.66z"/>
                          </svg>
                        </div>
                        <span className="text-sm text-blue-100/80 text-center">{t('portfolio.certifications.marketing_platform') || 'Google Marketing Platform'}</span>
                      </div>
                      <div className="bg-gray-800/50 p-4 rounded-lg border border-gray-700/50 flex flex-col items-center">
                        <div className="w-16 h-16 bg-yellow-500/20 rounded-full flex items-center justify-center mb-2">
                          <svg className="w-8 h-8 text-yellow-400" fill="currentColor" viewBox="0 0 24 24">
                            <path d="M12 2L4 5v6.09c0 5.05 3.41 9.76 8 10.91 4.59-1.15 8-5.86 8-10.91V5l-8-3zm-1.06 13.54L7.4 12l1.41-1.41 2.12 2.12 4.24-4.24 1.41 1.41-5.64 5.66z"/>
                          </svg>
                        </div>
                        <span className="text-sm text-blue-100/80 text-center">{t('portfolio.certifications.developers') || 'Google Developers'}</span>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Right Column: Contact Form */}
                <div className="lg:flex-1 bg-gray-800/50 backdrop-blur-sm rounded-2xl p-6 md:p-8 border border-gray-700/50 flex flex-col">
                  <h3 className="text-xl font-semibold text-white mb-6 text-center">{t('portfolio.form.title') || 'Send Us a Message'}</h3>
                  {submitStatus ? (
                    <div className={`p-4 rounded-lg ${submitStatus.success ? 'bg-green-900/30 border border-green-800' : 'bg-red-900/30 border border-red-800'}`}>
                      <p className={submitStatus.success ? 'text-green-300' : 'text-red-300'}>{submitStatus.message}</p>
                      {submitStatus.success && (
                        <button
                          onClick={() => { setSubmitStatus(null); reset(); } }
                          className="mt-4 text-sm text-blue-400 hover:text-blue-300 transition-colors w-full py-2 rounded-md bg-blue-600/20 hover:bg-blue-600/40"
                        >
                          {t('portfolio.form.send_another') || 'Send Another Message'}
                        </button>
                      )}
                    </div>
                  ) : (
                    <form onSubmit={handleSubmit(onSubmit)} className="space-y-5">
                      <div>
                        <label htmlFor="name" className="block text-sm font-medium text-gray-300 mb-1.5">
                          {t('portfolio.form.name_label') || 'Full Name'}
                        </label>
                        <input
                          type="text"
                          id="name"
                          {...register('name', { required: t('portfolio.form.name_required') || 'Full name is required' })}
                          className={`w-full px-4 py-2.5 rounded-lg bg-gray-700/60 border ${errors.name ? 'border-red-500' : 'border-gray-600'} focus:ring-2 focus:ring-blue-500 focus:border-blue-500 outline-none transition-colors placeholder-gray-500 text-white`}
                          placeholder={t('portfolio.form.name_placeholder') || 'Your Name'}
                        />
                        {errors.name && <p className="mt-1.5 text-xs text-red-400">{errors.name.message}</p>}
                      </div>

                      <div>
                        <label htmlFor="email" className="block text-sm font-medium text-gray-300 mb-1.5">
                          {t('portfolio.form.email_label') || 'Email Address'}
                        </label>
                        <input
                          type="email"
                          id="email"
                          {...register('email', {
                            required: t('portfolio.form.email_required') || 'Email is required',
                            pattern: { value: /^\S+@\S+$/i, message: t('portfolio.form.email_invalid') || 'Invalid email address' }
                          })}
                          className={`w-full px-4 py-2.5 rounded-lg bg-gray-700/60 border ${errors.email ? 'border-red-500' : 'border-gray-600'} focus:ring-2 focus:ring-blue-500 focus:border-blue-500 outline-none transition-colors placeholder-gray-500 text-white`}
                          placeholder={t('portfolio.form.email_placeholder') || '<EMAIL>'}
                        />
                        {errors.email && <p className="mt-1.5 text-xs text-red-400">{errors.email.message}</p>}
                      </div>

                      <div>
                        <label htmlFor="subject" className="block text-sm font-medium text-gray-300 mb-1.5">
                          {t('portfolio.form.subject_label') || 'Subject'}
                        </label>
                        <input
                          type="text"
                          id="subject"
                          {...register('subject', { required: t('portfolio.form.subject_required') || 'Subject is required' })}
                          className={`w-full px-4 py-2.5 rounded-lg bg-gray-700/60 border ${errors.subject ? 'border-red-500' : 'border-gray-600'} focus:ring-2 focus:ring-blue-500 focus:border-blue-500 outline-none transition-colors placeholder-gray-500 text-white`}
                          placeholder={t('portfolio.form.subject_placeholder') || 'How can we help?'}
                        />
                        {errors.subject && <p className="mt-1.5 text-xs text-red-400">{errors.subject.message}</p>}
                      </div>

                      <div>
                        <label htmlFor="message" className="block text-sm font-medium text-gray-300 mb-1.5">
                          {t('portfolio.form.message_label') || 'Message'}
                        </label>
                        <textarea
                          id="message"
                          rows={4}
                          {...register('message', { required: t('portfolio.form.message_required') || 'Message is required' })}
                          className={`w-full px-4 py-2.5 rounded-lg bg-gray-700/60 border ${errors.message ? 'border-red-500' : 'border-gray-600'} focus:ring-2 focus:ring-blue-500 focus:border-blue-500 outline-none transition-colors placeholder-gray-500 text-white`}
                          placeholder={t('portfolio.form.message_placeholder') || 'Your message...'}
                        ></textarea>
                        {errors.message && <p className="mt-1.5 text-xs text-red-400">{errors.message.message}</p>}
                      </div>

                      {/* Captcha */}
                      <div>
                        <label className="block text-sm font-medium text-gray-300 mb-2">
                          🔒 Security Verification
                        </label>
                        <SimpleCaptcha
                          onVerify={(isValid) => setCaptchaVerified(isValid)}
                          className="mb-4"
                        />
                      </div>

                      <div>
                        <button
                          type="submit"
                          disabled={isSubmitting || !captchaVerified}
                          className="w-full flex justify-center items-center px-6 py-3 rounded-lg bg-gradient-to-r from-blue-500 to-cyan-500 hover:from-blue-600 hover:to-cyan-600 text-white font-semibold text-base transition-all duration-300 ease-in-out shadow-md hover:shadow-lg disabled:opacity-70 disabled:cursor-not-allowed focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-gray-900 focus:ring-blue-500"
                        >
                          {isSubmitting ? (
                            <>
                              <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                              </svg>
                              {t('portfolio.form.sending') || 'Sending...'}
                            </>
                          ) : (t('portfolio.form.send_button') || 'Send Message')}
                        </button>
                      </div>
                    </form>
                  )}
                </div>
              </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default PortfolioPage;

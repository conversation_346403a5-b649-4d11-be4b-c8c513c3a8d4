import React from 'react';
import clsx from 'clsx';

interface ButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  variant?: 'primary' | 'secondary' | 'outline' | 'ghost';
  size?: 'sm' | 'md' | 'lg';
  children: React.ReactNode;
  className?: string;
}

const Button: React.FC<ButtonProps> = ({
  variant = 'primary',
  size = 'md',
  children,
  className,
  ...props
}) => {
  const baseClasses = 'inline-flex items-center justify-center font-medium transition-colors focus:outline-none';
  
  const variantClasses = {
    primary: 'bg-blue-600 text-white hover:bg-blue-700 rounded-md',
    secondary: 'bg-blue-600 text-white hover:bg-blue-700 rounded-md',
    outline: 'border border-neutral-300 bg-transparent text-neutral-800 hover:bg-neutral-50',
    ghost: 'bg-transparent text-neutral-800 hover:bg-neutral-100',
  };
  
  const sizeClasses = {
    sm: 'text-sm px-4 py-2',
    md: 'text-sm px-4 py-2',
    lg: 'text-base px-6 py-3',
  };
  
  const buttonClasses = clsx(
    baseClasses,
    variantClasses[variant],
    sizeClasses[size],
    className
  );
  
  return (
    <button className={buttonClasses} {...props}>
      {children}
    </button>
  );
};

export { Button };
export default Button;
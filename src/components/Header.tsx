import React, { useState, useEffect } from 'react';
import { Link, useNavigate, useLocation } from 'react-router-dom';
import { Menu, X, Bolt, LogOut, User } from 'lucide-react';
import Button from './ui/Button';
import { useAuth } from '../contexts/AuthContext';
import LanguageSwitcher from './ui/LanguageSwitcher';
import { useLanguage } from '../contexts/LanguageContext';

const Header: React.FC = () => {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const { isAuthenticated, logout } = useAuth();
  const { t } = useLanguage();
  const navigate = useNavigate();
  const location = useLocation();

  // Toggle mobile menu
  const toggleMenu = () => {
    setIsMenuOpen(!isMenuOpen);
  };

  // Close menu when route changes
  useEffect(() => {
    setIsMenuOpen(false);
  }, [location.pathname]);

  const handleLogout = () => {
    logout();
    navigate('/login');
  };

  // Dynamic navigation items with translations
  const navItems = [
    {
      id: 1,
      label: t('nav.services'),
      href: '/services',
    },
    {
      id: 2,
      label: t('nav.features'),
      href: '/features',
    },
    {
      id: 3,
      label: t('nav.pricing'),
      href: '/pricing',
    },
    {
      id: 5,
      label: t('nav.contact'),
      href: '/contact',
    },
    {
      id: 6,
      label: t('nav.blog'),
      href: '/blog',
    },
  ];

  return (
    <header className="fixed top-0 left-0 right-0 z-50 bg-gray-900/95 backdrop-blur-md shadow-lg py-3 border-b border-gray-800/70">
      <div className="container mx-auto px-4 md:px-8">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <Link to="/" className="flex items-center group">
              <Bolt className="h-7 w-7 text-blue-400 group-hover:text-blue-300 transition-colors" />
              <span className="ml-2 text-2xl font-bold bg-gradient-to-r from-blue-400 to-cyan-400 bg-clip-text text-transparent">gAds</span>
            </Link>
            <LanguageSwitcher />
          </div>

          {/* Desktop Navigation */}
          <nav className="hidden md:flex items-center space-x-6">
            {navItems.map((item) => {
              const isActive = location.pathname === item.href;
              const isExternal = item.href === '/blog';
              const url = item.href === '/blog' ? 'https://studiobanzai.com/blog-grid/' : item.href;
              
              return (
                <div key={item.id} className="group">
                  {isExternal ? (
                    <a
                      href={url}
                      target="_blank"
                      rel="noopener noreferrer"
                      className={`font-medium transition-colors px-3 py-2 rounded-md text-sm inline-block ${
                        'text-blue-100/70 hover:text-white hover:bg-blue-100/10'
                      }`}
                    >
                      <span className="relative">
                        {item.label}
                        <span className="absolute -bottom-1 left-0 h-0.5 bg-blue-600 transition-all w-0 group-hover:w-full"></span>
                      </span>
                    </a>
                  ) : (
                    <Link
                      to={url}
                      className={`font-medium transition-colors px-3 py-2 rounded-md text-sm inline-block ${
                        isActive 
                          ? 'text-white bg-blue-900/30' 
                          : 'text-blue-100 hover:text-white hover:bg-blue-100/10'
                      }`}
                    >
                      <span className="relative">
                        {item.label}
                        <span 
                          className={`absolute -bottom-1 left-0 h-0.5 bg-blue-600 transition-all ${
                            isActive ? 'w-full' : 'w-0 group-hover:w-full'
                          }`}
                        ></span>
                      </span>
                    </Link>
                  )}
                </div>
              );
            })}
            
            <Link 
              to="/portfolio"
              className="font-medium bg-gradient-to-r from-blue-500 to-cyan-500 hover:from-blue-600 hover:to-cyan-600 text-white px-4 py-2 rounded-md text-sm font-semibold transition-colors"
            >
              {t('nav.book_service')}
            </Link>
            
            {isAuthenticated ? (
              <div className="flex items-center space-x-2">
                <Link to="/dashboard">
                  <Button 
                    variant="secondary"
                    className="font-semibold flex items-center bg-blue-600 hover:bg-blue-700 text-white px-4 py-2"
                    onClick={() => navigate('/dashboard')}
                  >
                    <User className="mr-2 h-4 w-4" />
                    <span className="font-medium">{t('nav.dashboard')}</span>
                  </Button>
                </Link>
                <button
                  onClick={handleLogout}
                  className="p-2 text-neutral-700 hover:text-red-600 transition-colors"
                  aria-label={t('auth.logout')}
                >
                  <LogOut className="h-5 w-5" />
                </button>
              </div>
            ) : (
              <Link to="/login">
                <Button 
                  variant="secondary"
                  className="font-semibold"
                >
                  {t('auth.login')}
                </Button>
              </Link>
            )}
          </nav>

          {/* Mobile Get Started Button and Menu Button */}
          <div className="flex items-center md:hidden">
            <div className="flex items-center">
              {isAuthenticated && (
                <div className="flex items-center mr-2">
                  <Link to="/dashboard">
                    <Button 
                      variant="secondary"
                      className="font-semibold text-sm px-4 py-1.5 whitespace-nowrap flex items-center"
                      onClick={() => navigate('/dashboard')}
                    >
                      <User className="mr-1 h-3 w-3" />
                      Client Area
                    </Button>
                  </Link>
                  <button
                    onClick={handleLogout}
                    className="p-2 text-neutral-700 hover:text-red-600 transition-colors"
                    aria-label="Logout"
                  >
                    <LogOut className="h-4 w-4" />
                  </button>
                </div>
              )}
              {!isAuthenticated && (
                <Link to="/login" className="mr-2">
                  <Button 
                    variant="secondary"
                    className="font-semibold text-sm px-4 py-1.5 whitespace-nowrap"
                  >
                    Sign In
                  </Button>
                </Link>
              )}
              <button
                onClick={toggleMenu}
                className="inline-flex items-center justify-center p-2 rounded-md text-gray-400 hover:text-white hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-blue-500"
                aria-expanded={isMenuOpen}
                aria-label={isMenuOpen ? 'Close menu' : 'Open menu'}
              >
                {isMenuOpen ? (
                  <X className="h-6 w-6" aria-hidden="true" />
                ) : (
                  <Menu className="h-6 w-6" aria-hidden="true" />
                )}
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Mobile Navigation */}
      {isMenuOpen && (
        <div className="md:hidden bg-gray-900/95 backdrop-blur-md border-t border-gray-800/50 py-3 shadow-xl animate-fade-in">
          <div className="container mx-auto px-4">
            <nav className="flex flex-col space-y-1">
              {navItems.map((item) => {
                const isActive = location.pathname === item.href;
                const isExternal = item.href === '/blog';
                const url = item.href === '/blog' ? 'https://studiobanzai.com/blog-grid/' : item.href;
                
                if (isExternal) {
                  return (
                    <a
                      key={item.id}
                      href={url}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="block px-4 py-3 text-base font-medium rounded-md transition-colors text-blue-100/70 hover:bg-blue-100/10 hover:text-white"
                      onClick={() => setIsMenuOpen(false)}
                    >
                      {item.label}
                    </a>
                  );
                }
                
                return (
                  <Link
                    key={item.id}
                    to={url}
                    className={`block px-4 py-3 text-base font-medium rounded-md transition-colors ${
                      isActive 
                        ? 'text-white bg-blue-900/30' 
                        : 'text-blue-100/70 hover:bg-blue-100/10 hover:text-white'
                    }`}
                    onClick={() => setIsMenuOpen(false)}
                  >
                    {item.label}
                  </Link>
                );
              })}
              
              <Link
                to="/contact"
                className="block w-full text-center mt-4 px-4 py-3 bg-gradient-to-r from-blue-500 to-cyan-500 hover:from-blue-600 hover:to-cyan-600 text-white rounded-md font-medium transition-colors"
                onClick={() => setIsMenuOpen(false)}
              >
                {t('nav.book_service')}
              </Link>
            </nav>
          </div>
        </div>
      )}
    </header>
  );
};

export default Header;
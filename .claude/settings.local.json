{"permissions": {"allow": ["Bash(find:*)", "Bash(ls:*)", "<PERSON><PERSON>(docker exec:*)", "<PERSON><PERSON>(curl:*)", "Bash(git add:*)", "Bash(grep:*)", "Bash(cp:*)", "<PERSON><PERSON>(docker cp:*)", "<PERSON><PERSON>(docker restart:*)", "<PERSON><PERSON>(docker-compose exec:*)", "<PERSON><PERSON>(docker-compose up:*)", "Bash(npm run test-db:*)", "Bash(npm start)", "Bash(node:*)", "<PERSON><PERSON>(echo:*)", "Bash(BCRYPT_LIB=./backend/node_modules/bcrypt PG_LIB=./backend/node_modules/pg node -e \"\nconst bcrypt = require(''./backend/node_modules/bcrypt'');\nconst { Pool } = require(''./backend/node_modules/pg'');\n\nconst pool = new Pool({\n  user: ''postgres'',\n  host: ''localhost'',\n  database: ''gads_db'',\n  password: ''G4d5Str0ng'',\n  port: 5432,\n});\n\nasync function updatePasswords() {\n  try {\n    const adminHash = await bcrypt.hash(''admin123'', 10);\n    const userHash = await bcrypt.hash(''user123'', 10);\n    const demoHash = await bcrypt.hash(''demo123'', 10);\n    \n    await pool.query(''UPDATE users SET password_hash = $1 WHERE email = $2'', [adminHash, ''<EMAIL>'']);\n    await pool.query(''UPDATE users SET password_hash = $1 WHERE email = $2'', [userHash, ''<EMAIL>'']);\n    await pool.query(''UPDATE users SET password_hash = $1 WHERE email = $2'', [demoHash, ''<EMAIL>'']);\n    \n    console.log(''✅ Passwords updated successfully!'');\n    await pool.end();\n    process.exit(0);\n  } catch (error) {\n    console.error(''❌ Error:'', error);\n    process.exit(1);\n  }\n}\n\nupdatePasswords();\n\")", "mcp__ide__getDiagnostics", "Bash(npm run build:*)", "Bash(rm:*)", "<PERSON><PERSON>(docker:*)", "<PERSON><PERSON>(chmod:*)", "Bash(./start.sh:*)", "<PERSON><PERSON>(open:*)", "Bash(/dev/null)", "Bash(npm run dev:*)", "Bash(git commit:*)", "<PERSON><PERSON>(pkill:*)", "<PERSON><PERSON>(mv:*)", "Bash(npm install:*)", "Bash(npm audit:*)", "<PERSON><PERSON>(mkdir:*)", "Bash(PGPASSWORD=gads_password psql -h localhost -p 5432 -U gads_user -d gads_db -f ../add_missing_translations.sql)", "WebFetch(domain:api.gads-supercharge.online)", "Bash(psql:*)", "Bash(PGPASSWORD=supersecurepassword123456 psql -h localhost -U gads_user -d gads_db -c \"SELECT COUNT(*) FROM content_keys;\")", "Bash(PGPASSWORD=supersecurepassword123456 psql -h localhost -U gads_user -d gads_db -c \"SELECT COUNT(*) FROM content_translations;\")", "Bash(git reset:*)", "Bash(htpasswd:*)", "WebFetch(domain:github.com)"], "deny": []}}
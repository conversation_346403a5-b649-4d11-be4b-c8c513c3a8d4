const { Pool } = require('pg');

// Database connection for startup
const pool = new Pool({
  user: process.env.DB_USER || 'gads_user',
  host: process.env.DB_HOST || 'localhost',
  database: process.env.DB_NAME || 'gads_db',
  password: process.env.DB_PASSWORD || 'gads_password',
  port: process.env.DB_PORT || 5432,
});

async function fixColumnNames() {
  console.log('🔧 Fixing database schema and creating missing tables...');
  
  try {
    // Create admin_settings table if it doesn't exist
    await pool.query(`
      CREATE TABLE IF NOT EXISTS admin_settings (
        id SERIAL PRIMARY KEY,
        key VARCHAR(255) UNIQUE NOT NULL,
        value TEXT,
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
        updated_by UUID
      );
    `);
    console.log('✅ admin_settings table created/verified');
    
    // Create seo_settings table if it doesn't exist
    await pool.query(`
      CREATE TABLE IF NOT EXISTS seo_settings (
        id SERIAL PRIMARY KEY,
        page VARCHAR(255) UNIQUE NOT NULL,
        title_en VARCHAR(255),
        title_ua VARCHAR(255),
        description_en TEXT,
        description_ua TEXT,
        keywords_en TEXT,
        keywords_ua TEXT,
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
        updated_by UUID
      );
    `);
    console.log('✅ seo_settings table created/verified');
    
    // Insert default SEO settings
    await pool.query(`
      INSERT INTO seo_settings (page, title_en, title_ua, description_en, description_ua, keywords_en, keywords_ua)
      VALUES 
        ('home', 'gAds Supercharge - Google Ads Automation', 'gAds Supercharge - Автоматизація Google Ads', 
         'Professional Google Ads automation and management platform', 'Професійна платформа автоматизації та управління Google Ads',
         'google ads, automation, ppc, advertising', 'google ads, автоматизація, ppc, реклама'),
        ('dashboard', 'Dashboard - gAds Supercharge', 'Панель керування - gAds Supercharge',
         'Manage your Google Ads campaigns and tools', 'Управляйте вашими кампаніями Google Ads та інструментами',
         'dashboard, campaign management, tools', 'панель керування, управління кампаніями, інструменти')
      ON CONFLICT (page) DO NOTHING;
    `);
    console.log('✅ Default SEO settings inserted');
    
    // Fix user_activities columns if needed
    const result = await pool.query(`
      DO $$
      DECLARE
          action_exists boolean := false;
          details_exists boolean := false;
      BEGIN
          -- Check if old 'action' column exists
          SELECT EXISTS (
              SELECT 1 FROM information_schema.columns 
              WHERE table_name = 'user_activities' 
              AND column_name = 'action'
          ) INTO action_exists;
          
          -- Check if old 'details' column exists  
          SELECT EXISTS (
              SELECT 1 FROM information_schema.columns 
              WHERE table_name = 'user_activities' 
              AND column_name = 'details'
          ) INTO details_exists;
          
          -- Rename action to activity_type if it exists
          IF action_exists THEN
              ALTER TABLE user_activities RENAME COLUMN action TO activity_type;
              RAISE NOTICE 'Column action renamed to activity_type';
          END IF;
          
          -- Rename details to activity_data if it exists
          IF details_exists THEN
              ALTER TABLE user_activities RENAME COLUMN details TO activity_data;
              RAISE NOTICE 'Column details renamed to activity_data';
          END IF;
      END
      $$;
    `);
    
    console.log('✅ Database schema fixed successfully');
    
  } catch (error) {
    console.error('❌ Error fixing database schema:', error.message);
    // Don't throw error - let server start anyway
  } finally {
    await pool.end();
  }
}

module.exports = { fixColumnNames };
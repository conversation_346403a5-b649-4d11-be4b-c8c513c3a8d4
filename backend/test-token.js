const jwt = require('jsonwebtoken');

const user = {
  id: '6514ca18-3464-4c5f-834f-4c4f7adf81fc',
  email: '<EMAIL>',
  role: 'admin'
};

const token = jwt.sign(
  { 
    userId: user.id, 
    email: user.email, 
    role: user.role 
  },
  'your-super-secret-jwt-key-change-in-production-minimum-32-characters',
  { expiresIn: '24h' }
);

console.log('Token:', token);
console.log('Token length:', token.length);

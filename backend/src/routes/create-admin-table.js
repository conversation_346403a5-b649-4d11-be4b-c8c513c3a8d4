const express = require('express');
const { Pool } = require('pg');

const router = express.Router();

const pool = new Pool({
  user: process.env.DB_USER || 'gads_user',
  host: process.env.DB_HOST || 'localhost',
  database: process.env.DB_NAME || 'gads_db',
  password: process.env.DB_PASSWORD || 'gads_password',
  port: process.env.DB_PORT || 5432,
});

// Create admin_settings table
router.post('/create-admin-table', async (req, res) => {
  try {
    console.log('🔧 Creating admin_settings table...');
    
    // Create admin_settings table
    await pool.query(`
      CREATE TABLE IF NOT EXISTS admin_settings (
        id SERIAL PRIMARY KEY,
        key VARCHAR(255) UNIQUE NOT NULL,
        value TEXT,
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
        updated_by UUID REFERENCES users(id)
      );
    `);
    
    console.log('✅ admin_settings table created');
    
    // Create SEO settings table
    await pool.query(`
      CREATE TABLE IF NOT EXISTS seo_settings (
        id SERIAL PRIMARY KEY,
        page VARCHAR(255) UNIQUE NOT NULL,
        title_en VARCHAR(255),
        title_ua VARCHAR(255),
        description_en TEXT,
        description_ua TEXT,
        keywords_en TEXT,
        keywords_ua TEXT,
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
        updated_by UUID REFERENCES users(id)
      );
    `);
    
    console.log('✅ seo_settings table created');
    
    // Insert default SEO settings
    await pool.query(`
      INSERT INTO seo_settings (page, title_en, title_ua, description_en, description_ua, keywords_en, keywords_ua)
      VALUES 
        ('home', 'gAds Supercharge - Google Ads Automation', 'gAds Supercharge - Автоматизація Google Ads', 
         'Professional Google Ads automation and management platform', 'Професійна платформа автоматизації та управління Google Ads',
         'google ads, automation, ppc, advertising', 'google ads, автоматизація, ppc, реклама'),
        ('dashboard', 'Dashboard - gAds Supercharge', 'Панель керування - gAds Supercharge',
         'Manage your Google Ads campaigns and tools', 'Управляйте вашими кампаніями Google Ads та інструментами',
         'dashboard, campaign management, tools', 'панель керування, управління кампаніями, інструменти')
      ON CONFLICT (page) DO NOTHING;
    `);
    
    console.log('✅ Default SEO settings inserted');
    
    // Verify tables exist
    const tablesResult = await pool.query(`
      SELECT table_name 
      FROM information_schema.tables 
      WHERE table_schema = 'public' 
      AND table_name IN ('admin_settings', 'seo_settings')
      ORDER BY table_name;
    `);
    
    console.log('📋 Created tables:', tablesResult.rows);
    
    res.json({
      success: true,
      message: 'Admin tables created successfully',
      tables: tablesResult.rows
    });
    
  } catch (error) {
    console.error('❌ Error creating admin tables:', error);
    res.status(500).json({
      success: false,
      error: error.message,
      message: 'Failed to create admin tables'
    });
  }
});

module.exports = router;
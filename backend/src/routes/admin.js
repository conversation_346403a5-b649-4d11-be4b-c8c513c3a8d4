const express = require('express');
const bcrypt = require('bcryptjs');
const { Pool } = require('pg');
const { authenticateToken, requireAdmin } = require('../middleware/auth');

const router = express.Router();

const pool = new Pool({
  user: process.env.DB_USER || 'gads_user',
  host: process.env.DB_HOST || 'localhost',
  database: process.env.DB_NAME || 'gads_db',
  password: process.env.DB_PASSWORD || 'gads_password',
  port: process.env.DB_PORT || 5432,
});

// Apply authentication and admin check to all routes
router.use(authenticateToken);
router.use(requireAdmin);

// Tracking & Analytics Routes
router.get('/tracking-code', async (req, res) => {
  try {
    const result = await pool.query(
      'SELECT value FROM admin_settings WHERE key = $1',
      ['tracking_code']
    );
    
    res.json({ 
      code: result.rows.length > 0 ? result.rows[0].value : '' 
    });
  } catch (error) {
    console.error('Error fetching tracking code:', error);
    res.status(500).json({ error: 'Failed to fetch tracking code' });
  }
});

router.post('/tracking-code', async (req, res) => {
  try {
    const { code } = req.body;
    
    await pool.query(`
      INSERT INTO admin_settings (key, value, updated_at, updated_by) 
      VALUES ($1, $2, NOW(), $3)
      ON CONFLICT (key) DO UPDATE SET 
      value = EXCLUDED.value, 
      updated_at = NOW(), 
      updated_by = EXCLUDED.updated_by
    `, ['tracking_code', code, req.user?.userId || null]);

    // Log admin action
    if (req.user?.userId) {
      await pool.query(`
        INSERT INTO user_activities (user_id, activity_type, activity_data, created_at)
        VALUES ($1, $2, $3, NOW())
      `, [req.user.userId, 'update_tracking_code', JSON.stringify({ code_length: code.length })]);
    }

    res.json({ success: true });
  } catch (error) {
    console.error('Error saving tracking code:', error);
    res.status(500).json({ error: 'Failed to save tracking code' });
  }
});

// Verification Codes Routes
router.get('/verification-codes', async (req, res) => {
  try {
    const result = await pool.query(
      'SELECT value FROM admin_settings WHERE key = $1',
      ['verification_codes']
    );
    
    res.json({ 
      codes: result.rows.length > 0 ? result.rows[0].value : '' 
    });
  } catch (error) {
    console.error('Error fetching verification codes:', error);
    res.status(500).json({ error: 'Failed to fetch verification codes' });
  }
});

router.post('/verification-codes', async (req, res) => {
  try {
    const { codes } = req.body;
    
    await pool.query(`
      INSERT INTO admin_settings (key, value, updated_at, updated_by) 
      VALUES ($1, $2, NOW(), $3)
      ON CONFLICT (key) DO UPDATE SET 
      value = EXCLUDED.value, 
      updated_at = NOW(), 
      updated_by = EXCLUDED.updated_by
    `, ['verification_codes', codes, req.user?.userId || null]);

    // Log admin action
    if (req.user?.userId) {
      await pool.query(`
        INSERT INTO user_activities (user_id, activity_type, activity_data, created_at)
        VALUES ($1, $2, $3, NOW())
      `, [req.user.userId, 'update_verification_codes', JSON.stringify({ codes_length: codes.length })]);
    }

    res.json({ success: true });
  } catch (error) {
    console.error('Error saving verification codes:', error);
    res.status(500).json({ error: 'Failed to save verification codes' });
  }
});

// SEO Management Routes
router.get('/seo/:page', async (req, res) => {
  try {
    const { page } = req.params;
    
    // Validate page parameter to prevent injection
    const allowedPages = ['home', 'about', 'contact', 'services', 'blog', 'tools'];
    if (!allowedPages.includes(page)) {
      return res.status(400).json({ error: 'Invalid page specified' });
    }
    
    const result = await pool.query(
      'SELECT * FROM seo_settings WHERE page = $1',
      [page]
    );
    
    if (result.rows.length > 0) {
      res.json(result.rows[0]);
    } else {
      // Return default empty SEO data
      res.json({
        page,
        title_en: '',
        title_ua: '',
        description_en: '',
        description_ua: '',
        keywords_en: '',
        keywords_ua: ''
      });
    }
  } catch (error) {
    console.error('Error fetching SEO data:', error);
    res.status(500).json({ error: 'Failed to fetch SEO data' });
  }
});

router.post('/seo', async (req, res) => {
  try {
    const {
      page,
      title_en,
      title_ua,
      description_en,
      description_ua,
      keywords_en,
      keywords_ua
    } = req.body;
    
    // Validate page parameter
    const allowedPages = ['home', 'about', 'contact', 'services', 'blog', 'tools'];
    if (!allowedPages.includes(page)) {
      return res.status(400).json({ error: 'Invalid page specified' });
    }
    
    // Validate required fields
    if (!page) {
      return res.status(400).json({ error: 'Page is required' });
    }

    await pool.query(`
      INSERT INTO seo_settings (
        page, title_en, title_ua, description_en, description_ua,
        keywords_en, keywords_ua, updated_at, updated_by
      ) VALUES ($1, $2, $3, $4, $5, $6, $7, NOW(), $8)
      ON CONFLICT (page) DO UPDATE SET
        title_en = EXCLUDED.title_en,
        title_ua = EXCLUDED.title_ua,
        description_en = EXCLUDED.description_en,
        description_ua = EXCLUDED.description_ua,
        keywords_en = EXCLUDED.keywords_en,
        keywords_ua = EXCLUDED.keywords_ua,
        updated_at = NOW(),
        updated_by = EXCLUDED.updated_by
    `, [page, title_en, title_ua, description_en, description_ua, keywords_en, keywords_ua, req.user.userId]);

    // Log admin action
    if (req.user?.userId) {
      await pool.query(`
        INSERT INTO user_activities (user_id, activity_type, activity_data, created_at)
        VALUES ($1, $2, $3, NOW())
      `, [req.user.userId, 'update_seo_settings', JSON.stringify({ page })]);
    }

    res.json({ success: true });
  } catch (error) {
    console.error('Error saving SEO data:', error);
    res.status(500).json({ error: 'Failed to save SEO data' });
  }
});

// User Management Routes
router.get('/users', async (req, res) => {
  try {
    // First check if access_expiry_date column exists
    const columnCheck = await pool.query(`
      SELECT column_name
      FROM information_schema.columns
      WHERE table_name = 'users' AND column_name = 'access_expiry_date'
    `);

    const hasExpiryColumn = columnCheck.rows.length > 0;

    // Build query based on available columns
    const selectFields = hasExpiryColumn
      ? 'id, email, role, is_active, created_at, last_login_at, access_expiry_date'
      : 'id, email, role, is_active, created_at, last_login_at, NULL as access_expiry_date';

    const result = await pool.query(`
      SELECT ${selectFields}
      FROM users
      ORDER BY created_at DESC
    `);

    res.json(result.rows);
  } catch (error) {
    console.error('Error fetching users:', error);
    res.status(500).json({ error: 'Failed to fetch users' });
  }
});

router.post('/users', async (req, res) => {
  try {
    const { email, password, role, access_expiry_date } = req.body;
    
    // Input validation
    if (!email || !password || !role) {
      return res.status(400).json({ error: 'Email, password, and role are required' });
    }
    
    // Email format validation
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      return res.status(400).json({ error: 'Invalid email format' });
    }
    
    // Role validation
    const allowedRoles = ['user', 'admin', 'editor'];
    if (!allowedRoles.includes(role)) {
      return res.status(400).json({ error: 'Invalid role specified' });
    }
    
    // Password strength validation
    if (password.length < 8) {
      return res.status(400).json({ error: 'Password must be at least 8 characters long' });
    }
    
    // Check if user already exists
    const existingUser = await pool.query('SELECT id FROM users WHERE email = $1', [email]);
    if (existingUser.rows.length > 0) {
      return res.status(400).json({ error: 'User with this email already exists' });
    }

    // Hash password
    const hashedPassword = await bcrypt.hash(password, 12);

    // Check if access_expiry_date column exists
    const columnCheck = await pool.query(`
      SELECT column_name
      FROM information_schema.columns
      WHERE table_name = 'users' AND column_name = 'access_expiry_date'
    `);

    const hasExpiryColumn = columnCheck.rows.length > 0;

    let result;
    if (hasExpiryColumn) {
      result = await pool.query(`
        INSERT INTO users (email, password_hash, role, access_expiry_date, created_at, updated_at)
        VALUES ($1, $2, $3, $4, NOW(), NOW())
        RETURNING id, email, role, is_active, created_at, access_expiry_date
      `, [email, hashedPassword, role, access_expiry_date || null]);
    } else {
      result = await pool.query(`
        INSERT INTO users (email, password_hash, role, created_at, updated_at)
        VALUES ($1, $2, $3, NOW(), NOW())
        RETURNING id, email, role, is_active, created_at, NULL as access_expiry_date
      `, [email, hashedPassword, role]);
    }

    // Log admin action
    if (req.user?.userId) {
      await pool.query(`
        INSERT INTO user_activities (user_id, activity_type, activity_data, created_at)
        VALUES ($1, $2, $3, NOW())
      `, [req.user.userId, 'create_user', JSON.stringify({ created_user_email: email, role })]);
    }

    res.json(result.rows[0]);
  } catch (error) {
    console.error('Error creating user:', error);
    res.status(500).json({ error: 'Failed to create user' });
  }
});

router.put('/users/:userId', async (req, res) => {
  try {
    const { userId } = req.params;
    const updates = { ...req.body };
    
    console.log('Updating user:', userId, 'with data:', updates);
    
    // Validate user exists first
    const userCheck = await pool.query('SELECT id, email FROM users WHERE id = $1', [userId]);
    if (userCheck.rows.length === 0) {
      return res.status(404).json({ error: 'User not found' });
    }
    
    // Handle password separately if provided
    if (updates.password && updates.password.trim() !== '') {
      const hashedPassword = await bcrypt.hash(updates.password, 12);
      updates.password_hash = hashedPassword;
      delete updates.password; // Remove plain password
    }
    
    // Handle empty/null values for access_expiry_date
    if (updates.access_expiry_date === '' || updates.access_expiry_date === null) {
      updates.access_expiry_date = null;
    }
    
    // Check if email is already taken by another user
    if (updates.email) {
      const emailCheck = await pool.query('SELECT id FROM users WHERE email = $1 AND id != $2', [updates.email, userId]);
      if (emailCheck.rows.length > 0) {
        return res.status(400).json({ error: 'Email already taken by another user' });
      }
    }
    
    // Define allowed fields for security
    const allowedFields = ['email', 'role', 'is_active', 'access_expiry_date', 'password_hash'];
    const allowedRoles = ['user', 'admin', 'editor']; // Define valid roles
    
    // Build dynamic update query with whitelisted fields only
    const updateFields = [];
    const values = [];
    let paramCount = 1;

    Object.keys(updates).forEach(key => {
      if (allowedFields.includes(key)) {
        // Additional validation for role field
        if (key === 'role' && !allowedRoles.includes(updates[key])) {
          throw new Error('Invalid role specified');
        }
        // Additional validation for email field
        if (key === 'email' && updates[key]) {
          const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
          if (!emailRegex.test(updates[key])) {
            throw new Error('Invalid email format');
          }
        }
        
        updateFields.push(`${key} = $${paramCount}`);
        values.push(updates[key]);
        paramCount++;
      }
    });

    if (updateFields.length === 0) {
      return res.status(400).json({ error: 'No valid fields to update' });
    }

    updateFields.push(`updated_at = NOW()`);
    values.push(userId);

    // Check if access_expiry_date column exists
    const columnCheck = await pool.query(`
      SELECT column_name
      FROM information_schema.columns
      WHERE table_name = 'users' AND column_name = 'access_expiry_date'
    `);

    const hasExpiryColumn = columnCheck.rows.length > 0;
    const returnFields = hasExpiryColumn
      ? 'id, email, role, is_active, created_at, last_login_at, access_expiry_date'
      : 'id, email, role, is_active, created_at, last_login_at, NULL as access_expiry_date';

    const query = `
      UPDATE users
      SET ${updateFields.join(', ')}
      WHERE id = $${paramCount}
      RETURNING ${returnFields}
    `;

    console.log('Executing query:', query, 'with values:', values);
    const result = await pool.query(query, values);

    if (result.rows.length === 0) {
      return res.status(404).json({ error: 'User not found after update' });
    }

    // Log admin action
    if (req.user?.userId) {
      await pool.query(`
        INSERT INTO user_activities (user_id, activity_type, activity_data, created_at)
        VALUES ($1, $2, $3, NOW())
      `, [req.user.userId, 'update_user', JSON.stringify({ updated_user_id: userId, updates: Object.keys(req.body) })]);
    }

    console.log('User updated successfully:', result.rows[0]);
    res.json(result.rows[0]);
  } catch (error) {
    console.error('Error updating user:', error);
    
    // Handle specific validation errors
    if (error.message === 'Invalid role specified') {
      return res.status(400).json({ error: 'Invalid role specified' });
    }
    if (error.message === 'Invalid email format') {
      return res.status(400).json({ error: 'Invalid email format' });
    }
    
    // Generic error response without sensitive details
    res.status(500).json({ 
      error: 'Failed to update user'
    });
  }
});

// Content Management Routes
router.get('/content-keys', async (req, res) => {
  try {
    const result = await pool.query(`
      SELECT 
        ck.id,
        ck.key_name,
        ck.category,
        ck.description,
        COALESCE(
          json_object_agg(
            ct.language_code, ct.translation_text
          ) FILTER (WHERE ct.language_code IS NOT NULL),
          '{}'::json
        ) as translations
      FROM content_keys ck
      LEFT JOIN content_translations ct ON ck.id = ct.content_key_id
      GROUP BY ck.id, ck.key_name, ck.category, ck.description
      ORDER BY ck.category, ck.key_name
    `);
    
    res.json(result.rows);
  } catch (error) {
    console.error('Error fetching content keys:', error);
    res.status(500).json({ error: 'Failed to fetch content keys' });
  }
});

router.put('/content/:keyId', async (req, res) => {
  try {
    const { keyId } = req.params;
    const { translations } = req.body;

    // Update translations for each language
    for (const [languageCode, translationText] of Object.entries(translations)) {
      await pool.query(`
        INSERT INTO content_translations (content_key_id, language_code, translation_text, updated_at)
        VALUES ($1, $2, $3, NOW())
        ON CONFLICT (content_key_id, language_code) DO UPDATE SET
          translation_text = EXCLUDED.translation_text,
          updated_at = NOW()
      `, [keyId, languageCode, translationText]);
    }

    // Log admin action
    if (req.user?.userId) {
      await pool.query(`
        INSERT INTO user_activities (user_id, activity_type, activity_data, created_at)
        VALUES ($1, $2, $3, NOW())
      `, [req.user.userId, 'update_content_translation', JSON.stringify({ content_key_id: keyId, languages: Object.keys(translations) })]);
    }

    res.json({ success: true });
  } catch (error) {
    console.error('Error updating content:', error);
    res.status(500).json({ error: 'Failed to update content' });
  }
});

module.exports = router;

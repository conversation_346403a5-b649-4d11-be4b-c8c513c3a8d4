const express = require('express');
const { body, param, query, validationResult } = require('express-validator');
const { query: dbQuery } = require('../database/connection');
const { authenticateToken } = require('../middleware/auth');
const { logActivity } = require('../middleware/activityLogger');

const router = express.Router();

/**
 * Get all content for a specific language
 * GET /api/content/:language
 */
router.get('/:language', [
  param('language').isIn(['en', 'ua']).withMessage('Language must be en or ua'),
  query('category').optional().isString().withMessage('Category must be a string')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        error: 'Validation failed',
        details: errors.array()
      });
    }

    const { language } = req.params;
    const { category } = req.query;

    let queryText = `
      SELECT 
        ck.key_name,
        ck.category,
        ct.translation_text,
        ck.description
      FROM content_keys ck
      JOIN content_translations ct ON ck.id = ct.content_key_id
      WHERE ct.language_code = $1
    `;
    
    const queryParams = [language];

    if (category) {
      queryText += ' AND ck.category = $2';
      queryParams.push(category);
    }

    queryText += ' ORDER BY ck.category, ck.key_name';

    const result = await dbQuery(queryText, queryParams);

    // Transform to key-value object
    const content = {};
    result.rows.forEach(row => {
      content[row.key_name] = row.translation_text;
    });

    res.json({
      language,
      category: category || 'all',
      content,
      count: result.rows.length,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('Error fetching content:', error);
    res.status(500).json({
      error: 'Internal server error',
      message: 'Failed to fetch content'
    });
  }
});

/**
 * Get translations only (key-value pairs) for a specific language
 * GET /api/content/translations/:language
 */
router.get('/translations/:language', [
  param('language').isIn(['en', 'ua']).withMessage('Language must be en or ua')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        error: 'Validation failed',
        details: errors.array()
      });
    }

    const { language } = req.params;

    const queryText = `
      SELECT 
        ck.key_name,
        ct.translation_text
      FROM content_keys ck
      JOIN content_translations ct ON ck.id = ct.content_key_id
      WHERE ct.language_code = $1
      ORDER BY ck.key_name
    `;

    const result = await dbQuery(queryText, [language]);

    // Transform to simple key-value object
    const translations = {};
    result.rows.forEach(row => {
      translations[row.key_name] = row.translation_text;
    });

    res.json(translations);

  } catch (error) {
    console.error('Error fetching translations:', error);
    res.status(500).json({
      error: 'Internal server error',
      message: 'Failed to fetch translations'
    });
  }
});

/**
 * Get content by category for a specific language
 * GET /api/content/:language/category/:category
 */
router.get('/:language/category/:category', [
  param('language').isIn(['en', 'ua']).withMessage('Language must be en or ua'),
  param('category').isString().withMessage('Category is required')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        error: 'Validation failed',
        details: errors.array()
      });
    }

    const { language, category } = req.params;

    const queryText = `
      SELECT 
        ck.key_name,
        ct.translation_text,
        ck.description
      FROM content_keys ck
      JOIN content_translations ct ON ck.id = ct.content_key_id
      WHERE ct.language_code = $1 AND ck.category = $2
      ORDER BY ck.key_name
    `;

    const result = await dbQuery(queryText, [language, category]);

    const content = {};
    result.rows.forEach(row => {
      content[row.key_name] = row.translation_text;
    });

    res.json({
      language,
      category,
      content,
      count: result.rows.length,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('Error fetching category content:', error);
    res.status(500).json({
      error: 'Internal server error',
      message: 'Failed to fetch category content'
    });
  }
});

/**
 * Get available categories
 * GET /api/content/categories
 */
router.get('/meta/categories', async (req, res) => {
  try {
    const queryText = `
      SELECT DISTINCT category, COUNT(*) as key_count
      FROM content_keys
      GROUP BY category
      ORDER BY category
    `;

    const result = await dbQuery(queryText);

    res.json({
      categories: result.rows,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('Error fetching categories:', error);
    res.status(500).json({
      error: 'Internal server error',
      message: 'Failed to fetch categories'
    });
  }
});

/**
 * Create or update content (Admin only)
 * POST /api/content
 */
router.post('/', [
  authenticateToken,
  body('key_name').isString().notEmpty().withMessage('Key name is required'),
  body('category').isString().notEmpty().withMessage('Category is required'),
  body('translations').isObject().withMessage('Translations object is required'),
  body('description').optional().isString()
], logActivity('content_create'), async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        error: 'Validation failed',
        details: errors.array()
      });
    }

    // Check if user is admin
    if (req.user.role !== 'admin') {
      return res.status(403).json({
        error: 'Forbidden',
        message: 'Admin access required'
      });
    }

    const { key_name, category, translations, description } = req.body;

    // Start transaction
    const client = await require('../database/connection').getClient();
    
    try {
      await client.query('BEGIN');

      // Insert or update content key
      const keyResult = await client.query(`
        INSERT INTO content_keys (key_name, category, description)
        VALUES ($1, $2, $3)
        ON CONFLICT (key_name) 
        DO UPDATE SET category = $2, description = $3, updated_at = CURRENT_TIMESTAMP
        RETURNING id
      `, [key_name, category, description]);

      const contentKeyId = keyResult.rows[0].id;

      // Insert or update translations
      for (const [language, text] of Object.entries(translations)) {
        if (!['en', 'ua'].includes(language)) {
          throw new Error(`Invalid language code: ${language}`);
        }

        await client.query(`
          INSERT INTO content_translations (content_key_id, language_code, translation_text)
          VALUES ($1, $2, $3)
          ON CONFLICT (content_key_id, language_code)
          DO UPDATE SET translation_text = $3, updated_at = CURRENT_TIMESTAMP
        `, [contentKeyId, language, text]);
      }

      await client.query('COMMIT');

      res.status(201).json({
        message: 'Content created/updated successfully',
        key_name,
        category,
        translations: Object.keys(translations),
        timestamp: new Date().toISOString()
      });

    } catch (error) {
      await client.query('ROLLBACK');
      throw error;
    } finally {
      client.release();
    }

  } catch (error) {
    console.error('Error creating/updating content:', error);
    res.status(500).json({
      error: 'Internal server error',
      message: 'Failed to create/update content'
    });
  }
});

// Auto-populate missing content (admin only)
router.post('/auto-populate', authenticateToken, logActivity('content_populate'), async (req, res) => {
  try {
    // Check if user is admin
    if (req.user.role !== 'admin') {
      return res.status(403).json({
        error: 'Forbidden',
        message: 'Admin access required'
      });
    }

    const { addMissingContent } = require('../scripts/add-missing-content');
    await addMissingContent();

    res.json({
      success: true,
      message: 'Missing content added successfully'
    });
  } catch (error) {
    console.error('Auto-populate content error:', error);
    res.status(500).json({
      error: 'Internal server error',
      message: 'Failed to populate content'
    });
  }
});

// Check missing translations (admin only)
router.get('/check-missing', authenticateToken, async (req, res) => {
  try {
    // Check if user is admin
    if (req.user.role !== 'admin') {
      return res.status(403).json({
        error: 'Forbidden',
        message: 'Admin access required'
      });
    }

    const { checkMissingTranslations } = require('../scripts/check-missing-translations');
    const result = await checkMissingTranslations();

    res.json({
      success: true,
      data: result
    });
  } catch (error) {
    console.error('Check missing translations error:', error);
    res.status(500).json({
      error: 'Internal server error',
      message: 'Failed to check missing translations'
    });
  }
});

module.exports = router;

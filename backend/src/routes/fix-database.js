const express = require('express');
const { Pool } = require('pg');

const router = express.Router();

const pool = new Pool({
  user: process.env.DB_USER || 'gads_user',
  host: process.env.DB_HOST || 'localhost',
  database: process.env.DB_NAME || 'gads_db',
  password: process.env.DB_PASSWORD || 'gads_password',
  port: process.env.DB_PORT || 5432,
});

// Fix database schema endpoint
router.post('/fix-columns', async (req, res) => {
  try {
    console.log('🔧 Starting database column fix...');
    
    // Check current schema
    const checkResult = await pool.query(`
      SELECT column_name, data_type
      FROM information_schema.columns 
      WHERE table_name = 'user_activities' 
      AND column_name IN ('action', 'details', 'activity_type', 'activity_data')
      ORDER BY column_name;
    `);
    
    console.log('📋 Current schema:', checkResult.rows);
    
    const hasAction = checkResult.rows.some(row => row.column_name === 'action');
    const hasDetails = checkResult.rows.some(row => row.column_name === 'details');
    const hasActivityType = checkResult.rows.some(row => row.column_name === 'activity_type');
    const hasActivityData = checkResult.rows.some(row => row.column_name === 'activity_data');
    
    let changes = [];
    
    // Fix action -> activity_type
    if (hasAction && !hasActivityType) {
      await pool.query('ALTER TABLE user_activities RENAME COLUMN action TO activity_type');
      changes.push('action → activity_type');
      console.log('✅ Renamed action to activity_type');
    }
    
    // Fix details -> activity_data
    if (hasDetails && !hasActivityData) {
      await pool.query('ALTER TABLE user_activities RENAME COLUMN details TO activity_data');
      changes.push('details → activity_data');
      console.log('✅ Renamed details to activity_data');
    }
    
    // Verify final schema
    const finalResult = await pool.query(`
      SELECT column_name, data_type
      FROM information_schema.columns 
      WHERE table_name = 'user_activities' 
      AND column_name IN ('activity_type', 'activity_data')
      ORDER BY column_name;
    `);
    
    console.log('📋 Final schema:', finalResult.rows);
    
    res.json({
      success: true,
      changes,
      currentSchema: checkResult.rows,
      finalSchema: finalResult.rows,
      message: changes.length > 0 ? 'Database schema fixed successfully' : 'Database schema already correct'
    });
    
  } catch (error) {
    console.error('❌ Database fix error:', error);
    res.status(500).json({
      success: false,
      error: error.message,
      message: 'Failed to fix database schema'
    });
  }
});

// Check database schema endpoint
router.get('/check-schema', async (req, res) => {
  try {
    const result = await pool.query(`
      SELECT column_name, data_type, is_nullable
      FROM information_schema.columns 
      WHERE table_name = 'user_activities'
      ORDER BY column_name;
    `);
    
    res.json({
      success: true,
      columns: result.rows,
      hasCorrectSchema: result.rows.some(row => row.column_name === 'activity_type') &&
                      result.rows.some(row => row.column_name === 'activity_data')
    });
    
  } catch (error) {
    console.error('❌ Schema check error:', error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

module.exports = router;
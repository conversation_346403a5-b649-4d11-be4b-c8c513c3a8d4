const nodemailer = require('nodemailer');

/**
 * Email Service for sending emails through SMTP
 * Supports both MailHog (development) and production SMTP
 */
class EmailService {
  constructor() {
    this.transporter = null;
    this.initializeTransporter();
  }

  /**
   * Initialize the email transporter based on environment
   */
  initializeTransporter() {
    const isProduction = process.env.NODE_ENV === 'production';
    const useProductionSMTP = process.env.SMTP_RELAY_HOST && process.env.SMTP_USERNAME;

    if (isProduction && useProductionSMTP) {
      // Production SMTP (SendGrid, Mailgun, etc.)
      this.transporter = nodemailer.createTransport({
        host: process.env.SMTP_RELAY_HOST,
        port: 587,
        secure: false, // true for 465, false for other ports
        auth: {
          user: process.env.SMTP_USERNAME,
          pass: process.env.SMTP_PASSWORD,
        },
        tls: {
          rejectUnauthorized: false
        }
      });
      console.log('📧 Email service initialized with production SMTP');
    } else {
      // Development/Testing with MailHog
      this.transporter = nodemailer.createTransport({
        host: process.env.SMTP_HOST || 'mailhog',
        port: parseInt(process.env.SMTP_PORT) || 1025,
        secure: false,
        ignoreTLS: true,
        auth: false
      });
      console.log('📧 Email service initialized with MailHog (development)');
    }
  }

  /**
   * Send a contact form email
   * @param {Object} contactData - Contact form data
   * @param {string} contactData.name - Sender's name
   * @param {string} contactData.email - Sender's email
   * @param {string} contactData.subject - Email subject
   * @param {string} contactData.message - Email message
   */
  async sendContactEmail(contactData) {
    try {
      const { name, email, subject, message } = contactData;
      
      // Validate required fields
      if (!name || !email || !subject || !message) {
        throw new Error('Missing required contact form fields');
      }

      const emailOptions = {
        from: process.env.EMAIL_FROM || '<EMAIL>',
        to: process.env.ADMIN_EMAIL || '<EMAIL>',
        replyTo: email,
        subject: `Contact Form: ${subject}`,
        text: this.generateTextEmail(name, email, subject, message),
        html: this.generateHtmlEmail(name, email, subject, message)
      };

      const result = await this.transporter.sendMail(emailOptions);
      
      console.log('✅ Contact email sent successfully:', {
        messageId: result.messageId,
        from: email,
        subject: subject
      });

      return {
        success: true,
        messageId: result.messageId,
        message: 'Email sent successfully'
      };

    } catch (error) {
      console.error('❌ Failed to send contact email:', error);
      throw new Error(`Failed to send email: ${error.message}`);
    }
  }

  /**
   * Generate plain text email content
   */
  generateTextEmail(name, email, subject, message) {
    return `
New Contact Form Submission

From: ${name}
Email: ${email}
Subject: ${subject}

Message:
${message}

---
This email was sent from the gAds Supercharge contact form.
Reply directly to this email to respond to the sender.
    `.trim();
  }

  /**
   * Generate HTML email content
   */
  generateHtmlEmail(name, email, subject, message) {
    return `
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>Contact Form Submission</title>
    <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
        .container { max-width: 600px; margin: 0 auto; padding: 20px; }
        .header { background: #f8f9fa; padding: 20px; border-radius: 5px; margin-bottom: 20px; }
        .content { background: #fff; padding: 20px; border: 1px solid #ddd; border-radius: 5px; }
        .field { margin-bottom: 15px; }
        .label { font-weight: bold; color: #555; }
        .value { margin-top: 5px; }
        .message { background: #f8f9fa; padding: 15px; border-radius: 5px; margin-top: 10px; }
        .footer { margin-top: 20px; padding-top: 20px; border-top: 1px solid #ddd; font-size: 12px; color: #666; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h2>🚀 New Contact Form Submission</h2>
            <p>You have received a new message from the gAds Supercharge website.</p>
        </div>
        
        <div class="content">
            <div class="field">
                <div class="label">👤 Name:</div>
                <div class="value">${this.escapeHtml(name)}</div>
            </div>
            
            <div class="field">
                <div class="label">📧 Email:</div>
                <div class="value"><a href="mailto:${this.escapeHtml(email)}">${this.escapeHtml(email)}</a></div>
            </div>
            
            <div class="field">
                <div class="label">📝 Subject:</div>
                <div class="value">${this.escapeHtml(subject)}</div>
            </div>
            
            <div class="field">
                <div class="label">💬 Message:</div>
                <div class="message">${this.escapeHtml(message).replace(/\n/g, '<br>')}</div>
            </div>
        </div>
        
        <div class="footer">
            <p>This email was sent from the gAds Supercharge contact form.</p>
            <p>Reply directly to this email to respond to the sender.</p>
        </div>
    </div>
</body>
</html>
    `.trim();
  }

  /**
   * Escape HTML to prevent XSS
   */
  escapeHtml(text) {
    const map = {
      '&': '&amp;',
      '<': '&lt;',
      '>': '&gt;',
      '"': '&quot;',
      "'": '&#039;'
    };
    return text.replace(/[&<>"']/g, (m) => map[m]);
  }

  /**
   * Test email configuration
   */
  async testConnection() {
    try {
      await this.transporter.verify();
      console.log('✅ Email service connection verified');
      return true;
    } catch (error) {
      console.error('❌ Email service connection failed:', error);
      return false;
    }
  }
}

module.exports = new EmailService();

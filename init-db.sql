-- ===============================================================================
-- GADS SUPERCHARGE DATABASE SCHEMA - COMPLETE SETUP
-- ===============================================================================

-- Enable UUID extension
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Create users table
CREATE TABLE IF NOT EXISTS users (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    email VARCHAR(255) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    role VARCHAR(50) DEFAULT 'user',
    preferred_language VARCHAR(10) DEFAULT 'en',
    is_active BOOLEAN DEFAULT true,
    last_login_at TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create content_keys table
CREATE TABLE IF NOT EXISTS content_keys (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    key_name VARCHAR(255) UNIQUE NOT NULL,
    category VARCHAR(100) NOT NULL,
    description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create content_translations table
CREATE TABLE IF NOT EXISTS content_translations (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    content_key_id UUID REFERENCES content_keys(id) ON DELETE CASCADE,
    language_code VARCHAR(10) NOT NULL,
    translation TEXT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(content_key_id, language_code)
);

-- Create user_sessions table
CREATE TABLE IF NOT EXISTS user_sessions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    session_token VARCHAR(255) UNIQUE NOT NULL,
    browser_info JSONB,
    ip_address INET,
    user_agent TEXT,
    logout_time TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    expires_at TIMESTAMP NOT NULL,
    is_active BOOLEAN DEFAULT true
);

-- Create user_activities table
CREATE TABLE IF NOT EXISTS user_activities (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    session_id UUID REFERENCES user_sessions(id) ON DELETE SET NULL,
    activity_type VARCHAR(100) NOT NULL,
    activity_data JSONB,
    ip_address INET,
    user_agent TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create user_preferences table
CREATE TABLE IF NOT EXISTS user_preferences (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    preference_key VARCHAR(255) NOT NULL,
    preference_value JSONB,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(user_id, preference_key)
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_users_email ON users(email);
CREATE INDEX IF NOT EXISTS idx_users_role ON users(role);
CREATE INDEX IF NOT EXISTS idx_content_keys_category ON content_keys(category);
CREATE INDEX IF NOT EXISTS idx_content_keys_key_name ON content_keys(key_name);
CREATE INDEX IF NOT EXISTS idx_content_translations_language ON content_translations(language_code);
CREATE INDEX IF NOT EXISTS idx_user_sessions_user_id ON user_sessions(user_id);
CREATE INDEX IF NOT EXISTS idx_user_sessions_token ON user_sessions(session_token);
CREATE INDEX IF NOT EXISTS idx_user_activities_user_id ON user_activities(user_id);
CREATE INDEX IF NOT EXISTS idx_user_activities_type ON user_activities(activity_type);
CREATE INDEX IF NOT EXISTS idx_user_preferences_user_id ON user_preferences(user_id);

-- Insert default admin user
INSERT INTO users (id, email, password_hash, role, preferred_language, created_at, updated_at) 
VALUES (
    uuid_generate_v4(),
    '<EMAIL>',
    '$2b$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', -- Admin2025!Secure#
    'admin',
    'en',
    NOW(),
    NOW()
) ON CONFLICT (email) DO NOTHING;

-- Insert additional users
INSERT INTO users (id, email, password_hash, role, preferred_language, created_at, updated_at) 
VALUES 
    (uuid_generate_v4(), '<EMAIL>', '$2b$10$XeY8J9F.Qr5Z1K2L3M4N5O6P7Q8R9S0T1U2V3W4X5Y6Z7A8B9C0D1E', 'user', 'en', NOW(), NOW()),
    (uuid_generate_v4(), '<EMAIL>', '$2b$10$A1B2C3D4E5F6G7H8I9J0K1L2M3N4O5P6Q7R8S9T0U1V2W3X4Y5Z6A7', 'user', 'en', NOW(), NOW()),
    (uuid_generate_v4(), '<EMAIL>', '$2b$10$B2C3D4E5F6G7H8I9J0K1L2M3N4O5P6Q7R8S9T0U1V2W3X4Y5Z6A7B8', 'user', 'en', NOW(), NOW())
ON CONFLICT (email) DO NOTHING;

-- Insert basic content keys and translations
INSERT INTO content_keys (key_name, category, description) VALUES
    ('app.title', 'common', 'Application title'),
    ('nav.home', 'navigation', 'Home navigation'),
    ('nav.dashboard', 'navigation', 'Dashboard navigation'),
    ('nav.tools', 'navigation', 'Tools navigation'),
    ('auth.login', 'auth', 'Login button'),
    ('auth.logout', 'auth', 'Logout button'),
    ('auth.email', 'auth', 'Email field'),
    ('auth.password', 'auth', 'Password field')
ON CONFLICT (key_name) DO NOTHING;

-- Insert translations for basic keys
WITH content_key_ids AS (
    SELECT id, key_name FROM content_keys WHERE key_name IN (
        'app.title', 'nav.home', 'nav.dashboard', 'nav.tools',
        'auth.login', 'auth.logout', 'auth.email', 'auth.password'
    )
)
INSERT INTO content_translations (content_key_id, language_code, translation)
SELECT 
    ck.id,
    lang.code,
    CASE 
        WHEN ck.key_name = 'app.title' AND lang.code = 'en' THEN 'gAds Supercharge'
        WHEN ck.key_name = 'app.title' AND lang.code = 'ua' THEN 'gAds Supercharge'
        WHEN ck.key_name = 'nav.home' AND lang.code = 'en' THEN 'Home'
        WHEN ck.key_name = 'nav.home' AND lang.code = 'ua' THEN 'Головна'
        WHEN ck.key_name = 'nav.dashboard' AND lang.code = 'en' THEN 'Dashboard'
        WHEN ck.key_name = 'nav.dashboard' AND lang.code = 'ua' THEN 'Панель керування'
        WHEN ck.key_name = 'nav.tools' AND lang.code = 'en' THEN 'Tools'
        WHEN ck.key_name = 'nav.tools' AND lang.code = 'ua' THEN 'Інструменти'
        WHEN ck.key_name = 'auth.login' AND lang.code = 'en' THEN 'Login'
        WHEN ck.key_name = 'auth.login' AND lang.code = 'ua' THEN 'Увійти'
        WHEN ck.key_name = 'auth.logout' AND lang.code = 'en' THEN 'Logout'
        WHEN ck.key_name = 'auth.logout' AND lang.code = 'ua' THEN 'Вийти'
        WHEN ck.key_name = 'auth.email' AND lang.code = 'en' THEN 'Email'
        WHEN ck.key_name = 'auth.email' AND lang.code = 'ua' THEN 'Електронна пошта'
        WHEN ck.key_name = 'auth.password' AND lang.code = 'en' THEN 'Password'
        WHEN ck.key_name = 'auth.password' AND lang.code = 'ua' THEN 'Пароль'
    END as translation
FROM content_key_ids ck
CROSS JOIN (VALUES ('en'), ('ua')) AS lang(code)
ON CONFLICT (content_key_id, language_code) DO NOTHING;

-- Create trigger to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Apply triggers to tables with updated_at columns
CREATE TRIGGER update_users_updated_at BEFORE UPDATE ON users
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_content_keys_updated_at BEFORE UPDATE ON content_keys
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_content_translations_updated_at BEFORE UPDATE ON content_translations
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_user_preferences_updated_at BEFORE UPDATE ON user_preferences
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Log completion
SELECT 'Database schema created successfully!' as status;
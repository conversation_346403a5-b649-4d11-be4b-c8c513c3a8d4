# gAds Supercharge - User Credentials
# Updated: 2025-07-13 17:38 (БАЗА ДАНИХ ОНОВЛЕНА!)

## ✅ РОБОЧІ ПАРОЛІ В БАЗІ ДАНИХ

### Admin Account
Email: <EMAIL>
Password: Admin2025!Secure#
Role: admin
Status: ✅ АКТИВНИЙ В БД
Description: Full administrative access to all features and user management

### Standard User Account
Email: <EMAIL>
Password: User2025!Strong#
Role: user
Status: ✅ АКТИВНИЙ В БД
Description: Standard user access to all tools and features

### Demo Account
Email: ***************************б
Password: Demo2025!Test#
Role: user
Status: ✅ АКТИВНИЙ В БД
Description: Demo account for testing and demonstrations

### Test Account
Email: <EMAIL>
Password: Test2025!Complex#
Role: user
Status: ✅ АКТИВНИЙ В БД
Description: Additional test account for development

## Database Information
- Database: PostgreSQL
- Host: localhost:5432
- Database Name: gads_db
- Username: gads_user
- Password: gads_password

## API Endpoints
- Backend URL: http://localhost:3001
- Frontend URL: http://localhost:5176
- Login Endpoint: POST /api/auth/login
- Content API: GET /api/content/{language}

## Security Features
- JWT tokens with 24-hour expiration
- bcryptjs password hashing with salt rounds
- Session tracking with browser/device information
- Role-based access control
- Protected routes with authentication middleware

## Notes
- All passwords use strong complexity requirements
- Sessions are tracked in PostgreSQL database
- User activities are logged for security auditing
- CORS configured for both development ports (5173, 5176)

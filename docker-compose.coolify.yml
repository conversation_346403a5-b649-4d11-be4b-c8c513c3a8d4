version: '3.8'

# ===============================================================================
# GADS SUPERCHARGE - PRODUCTION DEPLOYMENT v1.47-admin-fix
# ===============================================================================
# 🎯 GOOGLE ADS AUTOMATION PLATFORM
# ✅ DOMAINS: gads-supercharge.online, api.gads-supercharge.online, mail.gads-supercharge.online
# ✅ FEATURES: 12+ Google Ads script generators, bilingual support (UK/EN), 487+ translations
# ✅ SERVICES: PostgreSQL, Redis, Backend API, Frontend React, MailHog Email
# 🔑 ADMIN LOGIN: <EMAIL> / Admin2025!Secure#
# 📧 EMAIL TEST: https://mail.gads-supercharge.online (admin/mailhog123)
#
# 🔐 БЕЗПЕЧНИЙ ДЕПЛОЙ v1.47-admin-fix:
# - АВТОМАТИЧНО: Користувачі створюються при старті backend контейнера
# - ВИПРАВЛЕНО: Admin API SQL queries для сумісності з різними схемами БД
# - ВИПРАВЛЕНО: User management functionality з правильним API URL
# - ЗАХИСТ: Математична капча + MailHog Nginx Auth (admin/mailhog123)
# - КОНТЕНТ: Автозаповнення відсутніх переводів
#
# 🔄 ВЕРСІЇ ОБРАЗІВ:
# - Backend: v1.47-admin-fix ✅ (ВИПРАВЛЕНО SQL queries + автоматичне створення таблиць)
# - Frontend: v1.46-user-fix ✅ (ВИПРАВЛЕНО user management + правильний API URL)
# - PostgreSQL: v1.41-force-fix ✅ (FORCE ALTER TABLE в існуючій БД)
# - MailHog: v1.0.0 ✅ (власний образ) + Nginx Proxy v1.0.1 з Basic Auth (admin/mailhog123)
#
# 📋 ПІСЛЯ ДЕПЛОЮ:
# ✅ НІЧОГО НЕ ТРЕБА РОБИТИ - ВСЕ АВТОМАТИЧНО!
# ===============================================================================

networks:
  default:
    external: true
    name: coolify

volumes:
  postgres_data:
  redis_data:
  mailhog_data:

services:
  postgres:
    # 🗄️ DATABASE - PostgreSQL 15 з правильною схемою
    # 🚨 v1.31-hotfix: Оновлена схема з колонкою activity_type
    image: aprokudan/gads-supercharge-postgres:v1.41-force-fix
    platform: linux/amd64
    restart: unless-stopped
    environment:
      POSTGRES_DB: gads_db
      POSTGRES_USER: gads_user
      POSTGRES_PASSWORD: supersecurepassword123456
      POSTGRES_HOST_AUTH_METHOD: trust
      PGDATA: /var/lib/postgresql/data/pgdata
    volumes:
      - postgres_data:/var/lib/postgresql/data
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U gads_user -d gads_db"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 60s
    labels:
      - "coolify.managed=true"

  redis:
    image: redis:7-alpine
    platform: linux/amd64
    restart: unless-stopped
    command: redis-server --appendonly yes --maxmemory 256mb --maxmemory-policy allkeys-lru
    volumes:
      - redis_data:/data
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3
    labels:
      - "coolify.managed=true"

  backend:
    # 🔧 BACKEND API - Node.js + Express + PostgreSQL
    # 🚨 v1.47-admin-fix: ВИПРАВЛЕНО SQL queries + автоматичне створення користувачів
    # 📋 БЕЗ РУЧНИХ ДІЙ: Користувачі створюються/оновлюються автоматично при старті
    # Option 1: Build from source (slower, always latest code)
    # build:
    #   context: .
    #   dockerfile: backend/Dockerfile
    # Option 2: Use pre-built image (faster, stable)
    image: aprokudan/gads-supercharge-backend:v1.47-admin-fix
    platform: linux/amd64
    restart: unless-stopped
    expose:
      - "3001"
    environment:
      NODE_ENV: production
      PORT: 3001
      DB_HOST: postgres
      DB_PORT: 5432
      DB_NAME: gads_db
      DB_USER: gads_user
      DB_PASSWORD: supersecurepassword123456
      JWT_SECRET: supersecurejwtsecret1234567890abcdef
      REDIS_HOST: redis
      REDIS_PORT: 6379
      CORS_ORIGIN: https://gads-supercharge.online,https://www.gads-supercharge.online
      SMTP_HOST: mailhog
      SMTP_PORT: 1025
      EMAIL_FROM: <EMAIL>
      ADMIN_EMAIL: <EMAIL>
      DOMAIN: gads-supercharge.online
      FRONTEND_URL: https://gads-supercharge.online
      API_URL: https://api.gads-supercharge.online
    depends_on:
      - postgres
      - redis
      - mailhog
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3001/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    labels:
      - "coolify.managed=true"
      - "coolify.url=api.gads-supercharge.online"
      - "coolify.port=3001"
      - "coolify.https=true"
      - "coolify.healthcheck.enabled=true"
      - "coolify.healthcheck.path=/health"

  frontend:
    # 🎨 FRONTEND - React + Vite + Nginx
    # 🚨 v1.46-user-fix: ВИПРАВЛЕНО user management + правильний API URL
    # 📋 Підключається до API: https://api.gads-supercharge.online/api
    # Option 1: Build from source (slower, always latest code)
    # build:
    #   context: .
    #   dockerfile: Dockerfile.frontend
    #   args:
    #     - VITE_API_URL=https://api.gads-supercharge.online/api
    # Option 2: Use pre-built image (faster, stable)
    image: aprokudan/gads-supercharge-frontend:v1.46-user-fix
    platform: linux/amd64
    restart: unless-stopped
    expose:
      - "80"
    depends_on:
      - backend
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:80/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 10s
    labels:
      - "coolify.managed=true"
      - "coolify.url=gads-supercharge.online"
      - "coolify.port=80"
      - "coolify.https=true"
      - "coolify.healthcheck.enabled=true"
      - "coolify.healthcheck.path=/health"

  mailhog:
    image: aprokudan/gads-supercharge-mailhog:v1.0.0
    platform: linux/amd64
    restart: unless-stopped
    expose:
      - "8025"
      - "1025"
    environment:
      MH_STORAGE: maildir
      MH_MAILDIR_PATH: /tmp
    volumes:
      - mailhog_data:/tmp
    command: ["MailHog"]
    healthcheck:
      test: ["CMD", "wget", "--quiet", "--tries=1", "--spider", "http://localhost:8025"]
      interval: 30s
      timeout: 10s
      retries: 3
    labels:
      - "coolify.managed=true"

  nginx-mailhog:
    image: aprokudan/gads-supercharge-nginx-mailhog:v1.0.1
    platform: linux/amd64
    restart: unless-stopped
    expose:
      - "80"
    depends_on:
      - mailhog
    healthcheck:
      test: ["CMD", "wget", "--quiet", "--tries=1", "--spider", "http://localhost:80"]
      interval: 30s
      timeout: 10s
      retries: 3
    labels:
      - "coolify.managed=true"
      - "coolify.url=mail.gads-supercharge.online"
      - "coolify.port=80"
      - "coolify.https=true"
      - "coolify.healthcheck.enabled=true"
      - "coolify.healthcheck.path=/"
const { Pool } = require('pg');

const pool = new Pool({
  user: 'gads_user',
  host: 'localhost',
  database: 'gads_db',
  password: 'gads_password',
  port: 5432,
});

const pageContent = [
  // Services page
  {
    key: 'services.title',
    category: 'services',
    description: 'Services page title',
    en: 'Our Services',
    ua: 'Наші послуги'
  },
  {
    key: 'services.subtitle',
    category: 'services',
    description: 'Services page subtitle',
    en: 'Professional Google Ads management and marketing solutions',
    ua: 'Професійне управління Google Ads та маркетингові рішення'
  },
  {
    key: 'services.description',
    category: 'services',
    description: 'Services page description',
    en: 'We provide comprehensive Google Ads management services to help your business grow and succeed online.',
    ua: 'Ми надаємо комплексні послуги з управління Google Ads, щоб допомогти вашому бізнесу рости та досягати успіху в інтернеті.'
  },

  // Features page
  {
    key: 'features.title',
    category: 'features',
    description: 'Features page title',
    en: 'Features',
    ua: 'Можливості'
  },
  {
    key: 'features.subtitle',
    category: 'features',
    description: 'Features page subtitle',
    en: 'Advanced Google Ads automation and optimization features',
    ua: 'Передові можливості автоматизації та оптимізації Google Ads'
  },
  {
    key: 'features.description',
    category: 'features',
    description: 'Features page description',
    en: 'Discover powerful features designed to maximize your advertising ROI and streamline campaign management.',
    ua: 'Відкрийте для себе потужні можливості, розроблені для максимізації ROI вашої реклами та оптимізації управління кампаніями.'
  },

  // Pricing page
  {
    key: 'pricing.title',
    category: 'pricing',
    description: 'Pricing page title',
    en: 'Pricing',
    ua: 'Тарифи'
  },
  {
    key: 'pricing.subtitle',
    category: 'pricing',
    description: 'Pricing page subtitle',
    en: 'Transparent pricing for Google Ads management services',
    ua: 'Прозора тарифікація для послуг з управління Google Ads'
  },
  {
    key: 'pricing.description',
    category: 'pricing',
    description: 'Pricing page description',
    en: 'Choose the perfect plan for your business needs with our transparent and affordable pricing options.',
    ua: 'Оберіть ідеальний план для потреб вашого бізнесу з нашими прозорими та доступними варіантами тарифів.'
  },

  // Contact page
  {
    key: 'contact.title',
    category: 'contact',
    description: 'Contact page title',
    en: 'Contact Us',
    ua: 'Зв\'яжіться з нами'
  },
  {
    key: 'contact.subtitle',
    category: 'contact',
    description: 'Contact page subtitle',
    en: 'Get in touch with our Google Ads experts',
    ua: 'Зв\'яжіться з нашими експертами Google Ads'
  },
  {
    key: 'contact.description',
    category: 'contact',
    description: 'Contact page description',
    en: 'Have questions about our services? Contact our team today for a free consultation and personalized recommendations.',
    ua: 'Маєте питання щодо наших послуг? Зв\'яжіться з нашою командою сьогодні для безкоштовної консультації та персоналізованих рекомендацій.'
  },

  // Portfolio page
  {
    key: 'portfolio.title',
    category: 'portfolio',
    description: 'Portfolio page title',
    en: 'Portfolio',
    ua: 'Портфоліо'
  },
  {
    key: 'portfolio.subtitle',
    category: 'portfolio',
    description: 'Portfolio page subtitle',
    en: 'Success stories and case studies',
    ua: 'Історії успіху та кейси'
  },
  {
    key: 'portfolio.description',
    category: 'portfolio',
    description: 'Portfolio page description',
    en: 'Explore our portfolio of successful Google Ads campaigns and see how we\'ve helped businesses achieve their marketing goals.',
    ua: 'Ознайомтеся з нашим портфоліо успішних кампаній Google Ads і подивіться, як ми допомогли бізнесам досягти їхніх маркетингових цілей.'
  },

  // About page
  {
    key: 'about.title',
    category: 'about',
    description: 'About page title',
    en: 'About Us',
    ua: 'Про нас'
  },
  {
    key: 'about.subtitle',
    category: 'about',
    description: 'About page subtitle',
    en: 'Google Ads certified experts with years of experience',
    ua: 'Сертифіковані експерти Google Ads з багаторічним досвідом'
  },
  {
    key: 'about.description',
    category: 'about',
    description: 'About page description',
    en: 'Meet our team of Google Ads certified professionals with extensive experience in digital marketing and advertising optimization.',
    ua: 'Познайомтеся з нашою командою сертифікованих професіоналів Google Ads з великим досвідом у цифровому маркетингу та оптимізації реклами.'
  },

  // Careers page
  {
    key: 'careers.title',
    category: 'careers',
    description: 'Careers page title',
    en: 'Careers',
    ua: 'Кар\'єра'
  },
  {
    key: 'careers.subtitle',
    category: 'careers',
    description: 'Careers page subtitle',
    en: 'Join our growing team of digital marketing experts',
    ua: 'Приєднуйтесь до нашої команди експертів цифрового маркетингу, що розвивається'
  },

  // Legal pages
  {
    key: 'privacy.title',
    category: 'legal',
    description: 'Privacy policy page title',
    en: 'Privacy Policy',
    ua: 'Політика конфіденційності'
  },
  {
    key: 'terms.title',
    category: 'legal',
    description: 'Terms of service page title',
    en: 'Terms of Service',
    ua: 'Умови надання послуг'
  },
  {
    key: 'cookies.title',
    category: 'legal',
    description: 'Cookies policy page title',
    en: 'Cookies Policy',
    ua: 'Політика використання файлів cookie'
  }
];

async function addPageContent() {
  const client = await pool.connect();
  
  try {
    console.log('Adding page content translations...');
    
    for (const content of pageContent) {
      // Insert content key
      const keyResult = await client.query(`
        INSERT INTO content_keys (key_name, category, description, created_at, updated_at)
        VALUES ($1, $2, $3, NOW(), NOW())
        ON CONFLICT (key_name) DO UPDATE SET
          category = EXCLUDED.category,
          description = EXCLUDED.description,
          updated_at = NOW()
        RETURNING id
      `, [content.key, content.category, content.description]);
      
      const keyId = keyResult.rows[0].id;
      console.log(`Added key: ${content.key} (${keyId})`);
      
      // Insert English translation
      await client.query(`
        INSERT INTO content_translations (content_key_id, language_code, translation_text, created_at, updated_at)
        VALUES ($1, 'en', $2, NOW(), NOW())
        ON CONFLICT (content_key_id, language_code) DO UPDATE SET
          translation_text = EXCLUDED.translation_text,
          updated_at = NOW()
      `, [keyId, content.en]);
      
      // Insert Ukrainian translation
      await client.query(`
        INSERT INTO content_translations (content_key_id, language_code, translation_text, created_at, updated_at)
        VALUES ($1, 'ua', $2, NOW(), NOW())
        ON CONFLICT (content_key_id, language_code) DO UPDATE SET
          translation_text = EXCLUDED.translation_text,
          updated_at = NOW()
      `, [keyId, content.ua]);
      
      console.log(`  EN: ${content.en}`);
      console.log(`  UA: ${content.ua}`);
    }
    
    console.log('✅ All page content added successfully!');
    console.log(`📊 Added ${pageContent.length} content keys`);
    
  } catch (error) {
    console.error('❌ Error adding page content:', error);
  } finally {
    client.release();
    await pool.end();
  }
}

addPageContent();
-- Add missing SEO and content keys for gAds Supercharge

-- SEO metadata keys
INSERT INTO content_keys (key_name, category, description) VALUES
('seo.meta_title_en', 'seo', 'English meta title for search engines'),
('seo.meta_title_ua', 'seo', 'Ukrainian meta title for search engines'),
('seo.meta_description_en', 'seo', 'English meta description for search results'),
('seo.meta_description_ua', 'seo', 'Ukrainian meta description for search results'),
('seo.keywords_en', 'seo', 'English keywords for SEO'),
('seo.keywords_ua', 'seo', 'Ukrainian keywords for SEO')
ON CONFLICT (key_name) DO NOTHING;

-- About page content
INSERT INTO content_keys (key_name, category, description) VALUES
('about.title', 'about', 'About page main title'),
('about.subtitle', 'about', 'About page subtitle'),
('about.description', 'about', 'About page description')
ON CONFLICT (key_name) DO NOTHING;

-- Careers page content
INSERT INTO content_keys (key_name, category, description) VALUES
('careers.title', 'careers', 'Careers page main title'),
('careers.subtitle', 'careers', 'Careers page subtitle'),
('careers.header.title', 'careers', 'Careers header title'),
('careers.header.subtitle', 'careers', 'Careers header subtitle'),
('careers.cta.text', 'careers', 'Careers call-to-action text'),
('careers.cta.button', 'careers', 'Careers call-to-action button text'),
('careers.positions.title', 'careers', 'Job positions section title'),
('careers.positions.developers.title', 'careers', 'Developers position title'),
('careers.positions.developers.description', 'careers', 'Developers position description'),
('careers.positions.sales.title', 'careers', 'Sales position title'),
('careers.positions.sales.description', 'careers', 'Sales position description'),
('careers.positions.testers.title', 'careers', 'Testers position title'),
('careers.positions.testers.description', 'careers', 'Testers position description'),
('careers.positions.influencers.title', 'careers', 'Influencers position title'),
('careers.positions.influencers.description', 'careers', 'Influencers position description')
ON CONFLICT (key_name) DO NOTHING;

-- Common elements
INSERT INTO content_keys (key_name, category, description) VALUES
('common.enableTelegram', 'common', 'Enable Telegram notifications text'),
('common.analyzeAndGenerate', 'common', 'Analyze and generate button text')
ON CONFLICT (key_name) DO NOTHING;

-- Add English translations
INSERT INTO content_translations (content_key_id, language_code, translation_text) 
SELECT ck.id, 'en', 
CASE ck.key_name
    -- SEO English
    WHEN 'seo.meta_title_en' THEN 'gAds Supercharge - Google Ads Automation & Script Generator Platform'
    WHEN 'seo.meta_description_en' THEN 'Professional Google Ads automation platform with 12+ script generators, budget optimization, performance analysis, and bilingual support. Boost your campaigns efficiency.'
    WHEN 'seo.keywords_en' THEN 'google ads, automation, script generator, budget optimization, campaign management, performance analysis, ppc tools'
    
    -- About English
    WHEN 'about.title' THEN 'About gAds Supercharge'
    WHEN 'about.subtitle' THEN 'Leading Google Ads Automation Platform'
    WHEN 'about.description' THEN 'We provide cutting-edge automation tools for Google Ads professionals, helping agencies and businesses optimize their campaigns with intelligent script generation and performance analysis.'
    
    -- Careers English
    WHEN 'careers.title' THEN 'Join Our Team'
    WHEN 'careers.subtitle' THEN 'Build the Future of Google Ads Automation'
    WHEN 'careers.header.title' THEN 'Careers at gAds Supercharge'
    WHEN 'careers.header.subtitle' THEN 'Shape the future of digital advertising automation'
    WHEN 'careers.cta.text' THEN 'Ready to revolutionize Google Ads automation with us?'
    WHEN 'careers.cta.button' THEN 'Apply Now'
    WHEN 'careers.positions.title' THEN 'Open Positions'
    WHEN 'careers.positions.developers.title' THEN 'Senior Full-Stack Developer'
    WHEN 'careers.positions.developers.description' THEN 'Build scalable automation tools and APIs for Google Ads management. React, Node.js, PostgreSQL experience required.'
    WHEN 'careers.positions.sales.title' THEN 'Sales Executive'
    WHEN 'careers.positions.sales.description' THEN 'Drive growth by connecting with agencies and businesses needing Google Ads automation solutions.'
    WHEN 'careers.positions.testers.title' THEN 'QA Automation Engineer'
    WHEN 'careers.positions.testers.description' THEN 'Ensure quality of our automation tools through comprehensive testing strategies and frameworks.'
    WHEN 'careers.positions.influencers.title' THEN 'Digital Marketing Specialist'
    WHEN 'careers.positions.influencers.description' THEN 'Create content and campaigns to showcase our Google Ads automation capabilities to the community.'
    
    -- Common English
    WHEN 'common.enableTelegram' THEN 'Enable Telegram Notifications'
    WHEN 'common.analyzeAndGenerate' THEN 'Analyze & Generate'
END
FROM content_keys ck
WHERE ck.key_name IN (
    'seo.meta_title_en', 'seo.meta_description_en', 'seo.keywords_en',
    'about.title', 'about.subtitle', 'about.description',
    'careers.title', 'careers.subtitle', 'careers.header.title', 'careers.header.subtitle',
    'careers.cta.text', 'careers.cta.button', 'careers.positions.title',
    'careers.positions.developers.title', 'careers.positions.developers.description',
    'careers.positions.sales.title', 'careers.positions.sales.description',
    'careers.positions.testers.title', 'careers.positions.testers.description',
    'careers.positions.influencers.title', 'careers.positions.influencers.description',
    'common.enableTelegram', 'common.analyzeAndGenerate'
)
ON CONFLICT (content_key_id, language_code) DO NOTHING;

-- Add Ukrainian translations
INSERT INTO content_translations (content_key_id, language_code, translation_text) 
SELECT ck.id, 'ua', 
CASE ck.key_name
    -- SEO Ukrainian
    WHEN 'seo.meta_title_ua' THEN 'gAds Supercharge - Платформа Автоматизації Google Ads і Генератор Скриптів'
    WHEN 'seo.meta_description_ua' THEN 'Професійна платформа автоматизації Google Ads з 12+ генераторами скриптів, оптимізацією бюджету, аналізом ефективності та двомовною підтримкою.'
    WHEN 'seo.keywords_ua' THEN 'google ads, автоматизація, генератор скриптів, оптимізація бюджету, управління кампаніями, аналіз ефективності, ppc інструменти'
    
    -- About Ukrainian
    WHEN 'about.title' THEN 'Про gAds Supercharge'
    WHEN 'about.subtitle' THEN 'Провідна Платформа Автоматизації Google Ads'
    WHEN 'about.description' THEN 'Ми надаємо передові інструменти автоматизації для професіоналів Google Ads, допомагаючи агенціям та бізнесу оптимізувати кампанії за допомогою інтелектуального генерування скриптів та аналізу ефективності.'
    
    -- Careers Ukrainian
    WHEN 'careers.title' THEN 'Приєднуйтесь до Нашої Команди'
    WHEN 'careers.subtitle' THEN 'Будуйте Майбутнє Автоматизації Google Ads'
    WHEN 'careers.header.title' THEN 'Кар\'єра в gAds Supercharge'
    WHEN 'careers.header.subtitle' THEN 'Формуйте майбутнє автоматизації цифрової реклами'
    WHEN 'careers.cta.text' THEN 'Готові революціонізувати автоматизацію Google Ads разом з нами?'
    WHEN 'careers.cta.button' THEN 'Подати Заявку'
    WHEN 'careers.positions.title' THEN 'Відкриті Позиції'
    WHEN 'careers.positions.developers.title' THEN 'Senior Full-Stack Розробник'
    WHEN 'careers.positions.developers.description' THEN 'Розробляйте масштабні інструменти автоматизації та API для управління Google Ads. Потрібен досвід з React, Node.js, PostgreSQL.'
    WHEN 'careers.positions.sales.title' THEN 'Менеджер з Продажу'
    WHEN 'careers.positions.sales.description' THEN 'Стимулюйте зростання, працюючи з агенціями та бізнесом, що потребують рішень автоматизації Google Ads.'
    WHEN 'careers.positions.testers.title' THEN 'Інженер QA Автоматизації'
    WHEN 'careers.positions.testers.description' THEN 'Забезпечуйте якість наших інструментів автоматизації через комплексні стратегії тестування.'
    WHEN 'careers.positions.influencers.title' THEN 'Спеціаліст з Цифрового Маркетингу'
    WHEN 'careers.positions.influencers.description' THEN 'Створюйте контент та кампанії для демонстрації можливостей нашої автоматизації Google Ads спільноті.'
    
    -- Common Ukrainian
    WHEN 'common.enableTelegram' THEN 'Увімкнути Telegram Сповіщення'
    WHEN 'common.analyzeAndGenerate' THEN 'Аналізувати та Генерувати'
END
FROM content_keys ck
WHERE ck.key_name IN (
    'seo.meta_title_ua', 'seo.meta_description_ua', 'seo.keywords_ua',
    'about.title', 'about.subtitle', 'about.description',
    'careers.title', 'careers.subtitle', 'careers.header.title', 'careers.header.subtitle',
    'careers.cta.text', 'careers.cta.button', 'careers.positions.title',
    'careers.positions.developers.title', 'careers.positions.developers.description',
    'careers.positions.sales.title', 'careers.positions.sales.description',
    'careers.positions.testers.title', 'careers.positions.testers.description',
    'careers.positions.influencers.title', 'careers.positions.influencers.description',
    'common.enableTelegram', 'common.analyzeAndGenerate'
)
ON CONFLICT (content_key_id, language_code) DO NOTHING;
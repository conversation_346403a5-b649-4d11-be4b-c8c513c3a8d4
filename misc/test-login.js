const bcrypt = require('bcryptjs');
const jwt = require('jsonwebtoken');
const { query } = require('./backend/src/database/connection');

async function testLogin() {
  try {
    console.log('Testing login flow...');
    
    // Step 1: Find user
    const userResult = await query(
      'SELECT id, email, password_hash, role, is_active, preferred_language FROM users WHERE email = $1',
      ['<EMAIL>']
    );
    
    console.log('User found:', userResult.rows.length > 0);
    
    if (userResult.rows.length === 0) {
      console.log('No user found');
      return;
    }
    
    const user = userResult.rows[0];
    console.log('User details:', {
      id: user.id,
      email: user.email,
      role: user.role,
      is_active: user.is_active
    });
    
    // Step 2: Check if user is active
    if (!user.is_active) {
      console.log('User is not active');
      return;
    }
    
    // Step 3: Verify password
    const passwordMatch = await bcrypt.compare('SuperAdmin2024!@#', user.password_hash);
    console.log('Password match:', passwordMatch);
    
    if (!passwordMatch) {
      console.log('Password does not match');
      return;
    }
    
    // Step 4: Generate JWT token
    const jwtSecret = process.env.JWT_SECRET || 'fallback-secret';
    console.log('JWT secret exists:', !!jwtSecret);
    
    const token = jwt.sign(
      { 
        userId: user.id, 
        email: user.email, 
        role: user.role 
      },
      jwtSecret,
      { expiresIn: '24h' }
    );
    
    console.log('Token generated:', !!token);
    console.log('Token length:', token.length);
    
    // Step 5: Update last login
    await query(
      'UPDATE users SET last_login_at = NOW() WHERE id = $1',
      [user.id]
    );
    
    console.log('Login successful - all steps completed');
    
    process.exit(0);
  } catch (error) {
    console.error('Error during login test:', error);
    process.exit(1);
  }
}

testLogin();
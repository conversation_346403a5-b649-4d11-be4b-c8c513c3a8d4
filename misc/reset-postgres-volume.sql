-- Quick fix: ALTER existing columns without recreating database
-- Run this directly in PostgreSQL to fix the schema

BEGIN;

-- Check if old columns exist and rename them
DO $$
BEGIN
    -- Rename action to activity_type if it exists
    IF EXISTS (SELECT 1 FROM information_schema.columns 
               WHERE table_name = 'user_activities' AND column_name = 'action') THEN
        ALTER TABLE user_activities RENAME COLUMN action TO activity_type;
    END IF;
    
    -- Rename details to activity_data if it exists  
    IF EXISTS (SELECT 1 FROM information_schema.columns 
               WHERE table_name = 'user_activities' AND column_name = 'details') THEN
        ALTER TABLE user_activities RENAME COLUMN details TO activity_data;
    END IF;
END
$$;

COMMIT;

-- Verify the changes
SELECT column_name, data_type 
FROM information_schema.columns 
WHERE table_name = 'user_activities' 
AND column_name IN ('activity_type', 'activity_data')
ORDER BY column_name;
--
-- PostgreSQL database dump
--

-- Dumped from database version 15.13
-- Dumped by pg_dump version 15.13

SET statement_timeout = 0;
SET lock_timeout = 0;
SET idle_in_transaction_session_timeout = 0;
SET client_encoding = 'UTF8';
SET standard_conforming_strings = on;
SELECT pg_catalog.set_config('search_path', '', false);
SET check_function_bodies = false;
SET xmloption = content;
SET client_min_messages = warning;
SET row_security = off;

DROP DATABASE IF EXISTS gads_db;
--
-- Name: gads_db; Type: DATABASE; Schema: -; Owner: gads_user
--

CREATE DATABASE gads_db WITH TEMPLATE = template0 ENCODING = 'UTF8' LOCALE_PROVIDER = libc LOCALE = 'en_US.utf8';


ALTER DATABASE gads_db OWNER TO gads_user;

\connect gads_db

SET statement_timeout = 0;
SET lock_timeout = 0;
SET idle_in_transaction_session_timeout = 0;
SET client_encoding = 'UTF8';
SET standard_conforming_strings = on;
SELECT pg_catalog.set_config('search_path', '', false);
SET check_function_bodies = false;
SET xmloption = content;
SET client_min_messages = warning;
SET row_security = off;

--
-- Name: uuid-ossp; Type: EXTENSION; Schema: -; Owner: -
--

CREATE EXTENSION IF NOT EXISTS "uuid-ossp" WITH SCHEMA public;


--
-- Name: EXTENSION "uuid-ossp"; Type: COMMENT; Schema: -; Owner: 
--

COMMENT ON EXTENSION "uuid-ossp" IS 'generate universally unique identifiers (UUIDs)';


--
-- Name: update_updated_at_column(); Type: FUNCTION; Schema: public; Owner: gads_user
--

CREATE FUNCTION public.update_updated_at_column() RETURNS trigger
    LANGUAGE plpgsql
    AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$;


ALTER FUNCTION public.update_updated_at_column() OWNER TO gads_user;

SET default_tablespace = '';

SET default_table_access_method = heap;

--
-- Name: admin_settings; Type: TABLE; Schema: public; Owner: gads_user
--

CREATE TABLE public.admin_settings (
    id uuid DEFAULT public.uuid_generate_v4() NOT NULL,
    key character varying(255) NOT NULL,
    value text,
    description text,
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    updated_by uuid
);


ALTER TABLE public.admin_settings OWNER TO gads_user;

--
-- Name: content_keys; Type: TABLE; Schema: public; Owner: gads_user
--

CREATE TABLE public.content_keys (
    id uuid DEFAULT public.uuid_generate_v4() NOT NULL,
    key_name character varying(255) NOT NULL,
    category character varying(100) NOT NULL,
    description text,
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE public.content_keys OWNER TO gads_user;

--
-- Name: content_translations; Type: TABLE; Schema: public; Owner: gads_user
--

CREATE TABLE public.content_translations (
    id uuid DEFAULT public.uuid_generate_v4() NOT NULL,
    content_key_id uuid NOT NULL,
    language_code character varying(5) NOT NULL,
    translation_text text NOT NULL,
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT content_translations_language_code_check CHECK (((language_code)::text = ANY ((ARRAY['en'::character varying, 'ua'::character varying])::text[])))
);


ALTER TABLE public.content_translations OWNER TO gads_user;

--
-- Name: seo_settings; Type: TABLE; Schema: public; Owner: gads_user
--

CREATE TABLE public.seo_settings (
    id uuid DEFAULT public.uuid_generate_v4() NOT NULL,
    page character varying(100) NOT NULL,
    title_en character varying(255),
    title_ua character varying(255),
    description_en text,
    description_ua text,
    keywords_en text,
    keywords_ua text,
    verification_codes text,
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    updated_by uuid
);


ALTER TABLE public.seo_settings OWNER TO gads_user;

--
-- Name: user_activities; Type: TABLE; Schema: public; Owner: gads_user
--

CREATE TABLE public.user_activities (
    id uuid DEFAULT public.uuid_generate_v4() NOT NULL,
    user_id uuid NOT NULL,
    session_id uuid,
    activity_type character varying(100) NOT NULL,
    activity_data jsonb,
    ip_address inet,
    user_agent text,
    page_url text,
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE public.user_activities OWNER TO gads_user;

--
-- Name: user_preferences; Type: TABLE; Schema: public; Owner: gads_user
--

CREATE TABLE public.user_preferences (
    id uuid DEFAULT public.uuid_generate_v4() NOT NULL,
    user_id uuid NOT NULL,
    preference_key character varying(100) NOT NULL,
    preference_value jsonb NOT NULL,
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE public.user_preferences OWNER TO gads_user;

--
-- Name: user_sessions; Type: TABLE; Schema: public; Owner: gads_user
--

CREATE TABLE public.user_sessions (
    id uuid DEFAULT public.uuid_generate_v4() NOT NULL,
    user_id uuid NOT NULL,
    session_token character varying(255) NOT NULL,
    ip_address inet,
    user_agent text,
    browser_info jsonb,
    login_time timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    logout_time timestamp with time zone,
    is_active boolean DEFAULT true,
    expires_at timestamp with time zone NOT NULL
);


ALTER TABLE public.user_sessions OWNER TO gads_user;

--
-- Name: users; Type: TABLE; Schema: public; Owner: gads_user
--

CREATE TABLE public.users (
    id uuid DEFAULT public.uuid_generate_v4() NOT NULL,
    email character varying(255) NOT NULL,
    password_hash character varying(255) NOT NULL,
    role character varying(50) DEFAULT 'user'::character varying,
    is_active boolean DEFAULT true,
    preferred_language character varying(5) DEFAULT 'en'::character varying,
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    last_login_at timestamp with time zone,
    access_expiry_date timestamp with time zone,
    CONSTRAINT users_preferred_language_check CHECK (((preferred_language)::text = ANY ((ARRAY['en'::character varying, 'ua'::character varying])::text[]))),
    CONSTRAINT users_role_check CHECK (((role)::text = ANY ((ARRAY['admin'::character varying, 'user'::character varying])::text[])))
);


ALTER TABLE public.users OWNER TO gads_user;

--
-- Name: admin_settings admin_settings_key_key; Type: CONSTRAINT; Schema: public; Owner: gads_user
--

ALTER TABLE ONLY public.admin_settings
    ADD CONSTRAINT admin_settings_key_key UNIQUE (key);


--
-- Name: admin_settings admin_settings_pkey; Type: CONSTRAINT; Schema: public; Owner: gads_user
--

ALTER TABLE ONLY public.admin_settings
    ADD CONSTRAINT admin_settings_pkey PRIMARY KEY (id);


--
-- Name: content_keys content_keys_key_name_key; Type: CONSTRAINT; Schema: public; Owner: gads_user
--

ALTER TABLE ONLY public.content_keys
    ADD CONSTRAINT content_keys_key_name_key UNIQUE (key_name);


--
-- Name: content_keys content_keys_pkey; Type: CONSTRAINT; Schema: public; Owner: gads_user
--

ALTER TABLE ONLY public.content_keys
    ADD CONSTRAINT content_keys_pkey PRIMARY KEY (id);


--
-- Name: content_translations content_translations_content_key_id_language_code_key; Type: CONSTRAINT; Schema: public; Owner: gads_user
--

ALTER TABLE ONLY public.content_translations
    ADD CONSTRAINT content_translations_content_key_id_language_code_key UNIQUE (content_key_id, language_code);


--
-- Name: content_translations content_translations_pkey; Type: CONSTRAINT; Schema: public; Owner: gads_user
--

ALTER TABLE ONLY public.content_translations
    ADD CONSTRAINT content_translations_pkey PRIMARY KEY (id);


--
-- Name: seo_settings seo_settings_page_key; Type: CONSTRAINT; Schema: public; Owner: gads_user
--

ALTER TABLE ONLY public.seo_settings
    ADD CONSTRAINT seo_settings_page_key UNIQUE (page);


--
-- Name: seo_settings seo_settings_pkey; Type: CONSTRAINT; Schema: public; Owner: gads_user
--

ALTER TABLE ONLY public.seo_settings
    ADD CONSTRAINT seo_settings_pkey PRIMARY KEY (id);


--
-- Name: user_activities user_activities_pkey; Type: CONSTRAINT; Schema: public; Owner: gads_user
--

ALTER TABLE ONLY public.user_activities
    ADD CONSTRAINT user_activities_pkey PRIMARY KEY (id);


--
-- Name: user_preferences user_preferences_pkey; Type: CONSTRAINT; Schema: public; Owner: gads_user
--

ALTER TABLE ONLY public.user_preferences
    ADD CONSTRAINT user_preferences_pkey PRIMARY KEY (id);


--
-- Name: user_preferences user_preferences_user_id_preference_key_key; Type: CONSTRAINT; Schema: public; Owner: gads_user
--

ALTER TABLE ONLY public.user_preferences
    ADD CONSTRAINT user_preferences_user_id_preference_key_key UNIQUE (user_id, preference_key);


--
-- Name: user_sessions user_sessions_pkey; Type: CONSTRAINT; Schema: public; Owner: gads_user
--

ALTER TABLE ONLY public.user_sessions
    ADD CONSTRAINT user_sessions_pkey PRIMARY KEY (id);


--
-- Name: user_sessions user_sessions_session_token_key; Type: CONSTRAINT; Schema: public; Owner: gads_user
--

ALTER TABLE ONLY public.user_sessions
    ADD CONSTRAINT user_sessions_session_token_key UNIQUE (session_token);


--
-- Name: users users_email_key; Type: CONSTRAINT; Schema: public; Owner: gads_user
--

ALTER TABLE ONLY public.users
    ADD CONSTRAINT users_email_key UNIQUE (email);


--
-- Name: users users_pkey; Type: CONSTRAINT; Schema: public; Owner: gads_user
--

ALTER TABLE ONLY public.users
    ADD CONSTRAINT users_pkey PRIMARY KEY (id);


--
-- Name: idx_admin_settings_key; Type: INDEX; Schema: public; Owner: gads_user
--

CREATE INDEX idx_admin_settings_key ON public.admin_settings USING btree (key);


--
-- Name: idx_content_keys_category; Type: INDEX; Schema: public; Owner: gads_user
--

CREATE INDEX idx_content_keys_category ON public.content_keys USING btree (category);


--
-- Name: idx_content_keys_name; Type: INDEX; Schema: public; Owner: gads_user
--

CREATE INDEX idx_content_keys_name ON public.content_keys USING btree (key_name);


--
-- Name: idx_content_translations_key_lang; Type: INDEX; Schema: public; Owner: gads_user
--

CREATE INDEX idx_content_translations_key_lang ON public.content_translations USING btree (content_key_id, language_code);


--
-- Name: idx_content_translations_language; Type: INDEX; Schema: public; Owner: gads_user
--

CREATE INDEX idx_content_translations_language ON public.content_translations USING btree (language_code);


--
-- Name: idx_seo_settings_page; Type: INDEX; Schema: public; Owner: gads_user
--

CREATE INDEX idx_seo_settings_page ON public.seo_settings USING btree (page);


--
-- Name: idx_user_activities_created; Type: INDEX; Schema: public; Owner: gads_user
--

CREATE INDEX idx_user_activities_created ON public.user_activities USING btree (created_at);


--
-- Name: idx_user_activities_session_id; Type: INDEX; Schema: public; Owner: gads_user
--

CREATE INDEX idx_user_activities_session_id ON public.user_activities USING btree (session_id);


--
-- Name: idx_user_activities_type; Type: INDEX; Schema: public; Owner: gads_user
--

CREATE INDEX idx_user_activities_type ON public.user_activities USING btree (activity_type);


--
-- Name: idx_user_activities_user_id; Type: INDEX; Schema: public; Owner: gads_user
--

CREATE INDEX idx_user_activities_user_id ON public.user_activities USING btree (user_id);


--
-- Name: idx_user_preferences_user_key; Type: INDEX; Schema: public; Owner: gads_user
--

CREATE INDEX idx_user_preferences_user_key ON public.user_preferences USING btree (user_id, preference_key);


--
-- Name: idx_user_sessions_active; Type: INDEX; Schema: public; Owner: gads_user
--

CREATE INDEX idx_user_sessions_active ON public.user_sessions USING btree (is_active);


--
-- Name: idx_user_sessions_expires; Type: INDEX; Schema: public; Owner: gads_user
--

CREATE INDEX idx_user_sessions_expires ON public.user_sessions USING btree (expires_at);


--
-- Name: idx_user_sessions_token; Type: INDEX; Schema: public; Owner: gads_user
--

CREATE INDEX idx_user_sessions_token ON public.user_sessions USING btree (session_token);


--
-- Name: idx_user_sessions_user_id; Type: INDEX; Schema: public; Owner: gads_user
--

CREATE INDEX idx_user_sessions_user_id ON public.user_sessions USING btree (user_id);


--
-- Name: idx_users_access_expiry; Type: INDEX; Schema: public; Owner: gads_user
--

CREATE INDEX idx_users_access_expiry ON public.users USING btree (access_expiry_date);


--
-- Name: idx_users_active; Type: INDEX; Schema: public; Owner: gads_user
--

CREATE INDEX idx_users_active ON public.users USING btree (is_active);


--
-- Name: idx_users_email; Type: INDEX; Schema: public; Owner: gads_user
--

CREATE INDEX idx_users_email ON public.users USING btree (email);


--
-- Name: idx_users_role; Type: INDEX; Schema: public; Owner: gads_user
--

CREATE INDEX idx_users_role ON public.users USING btree (role);


--
-- Name: admin_settings update_admin_settings_updated_at; Type: TRIGGER; Schema: public; Owner: gads_user
--

CREATE TRIGGER update_admin_settings_updated_at BEFORE UPDATE ON public.admin_settings FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();


--
-- Name: content_keys update_content_keys_updated_at; Type: TRIGGER; Schema: public; Owner: gads_user
--

CREATE TRIGGER update_content_keys_updated_at BEFORE UPDATE ON public.content_keys FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();


--
-- Name: content_translations update_content_translations_updated_at; Type: TRIGGER; Schema: public; Owner: gads_user
--

CREATE TRIGGER update_content_translations_updated_at BEFORE UPDATE ON public.content_translations FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();


--
-- Name: seo_settings update_seo_settings_updated_at; Type: TRIGGER; Schema: public; Owner: gads_user
--

CREATE TRIGGER update_seo_settings_updated_at BEFORE UPDATE ON public.seo_settings FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();


--
-- Name: user_preferences update_user_preferences_updated_at; Type: TRIGGER; Schema: public; Owner: gads_user
--

CREATE TRIGGER update_user_preferences_updated_at BEFORE UPDATE ON public.user_preferences FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();


--
-- Name: users update_users_updated_at; Type: TRIGGER; Schema: public; Owner: gads_user
--

CREATE TRIGGER update_users_updated_at BEFORE UPDATE ON public.users FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();


--
-- Name: admin_settings admin_settings_updated_by_fkey; Type: FK CONSTRAINT; Schema: public; Owner: gads_user
--

ALTER TABLE ONLY public.admin_settings
    ADD CONSTRAINT admin_settings_updated_by_fkey FOREIGN KEY (updated_by) REFERENCES public.users(id);


--
-- Name: content_translations content_translations_content_key_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: gads_user
--

ALTER TABLE ONLY public.content_translations
    ADD CONSTRAINT content_translations_content_key_id_fkey FOREIGN KEY (content_key_id) REFERENCES public.content_keys(id) ON DELETE CASCADE;


--
-- Name: seo_settings seo_settings_updated_by_fkey; Type: FK CONSTRAINT; Schema: public; Owner: gads_user
--

ALTER TABLE ONLY public.seo_settings
    ADD CONSTRAINT seo_settings_updated_by_fkey FOREIGN KEY (updated_by) REFERENCES public.users(id);


--
-- Name: user_activities user_activities_session_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: gads_user
--

ALTER TABLE ONLY public.user_activities
    ADD CONSTRAINT user_activities_session_id_fkey FOREIGN KEY (session_id) REFERENCES public.user_sessions(id) ON DELETE SET NULL;


--
-- Name: user_activities user_activities_user_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: gads_user
--

ALTER TABLE ONLY public.user_activities
    ADD CONSTRAINT user_activities_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.users(id) ON DELETE CASCADE;


--
-- Name: user_preferences user_preferences_user_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: gads_user
--

ALTER TABLE ONLY public.user_preferences
    ADD CONSTRAINT user_preferences_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.users(id) ON DELETE CASCADE;


--
-- Name: user_sessions user_sessions_user_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: gads_user
--

ALTER TABLE ONLY public.user_sessions
    ADD CONSTRAINT user_sessions_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.users(id) ON DELETE CASCADE;


--
-- PostgreSQL database dump complete
--


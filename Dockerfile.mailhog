# ===============================================================================
# GADS SUPERCHARGE - CUSTOM MAILHOG IMAGE
# ===============================================================================
# 🎯 CUSTOM MAILHOG WITH GADS BRANDING
# ✅ BASE: Official MailHog image
# ✅ SMTP: Port 1025 for backend email sending
# ✅ WEB: Port 8025 for web interface
# ===============================================================================

FROM mailhog/mailhog:latest

# Metadata
LABEL maintainer="<EMAIL>"
LABEL description="Custom MailHog for gAds-supercharge project"
LABEL version="v1.0.0"

# Environment variables
ENV MH_STORAGE=maildir
ENV MH_MAILDIR_PATH=/tmp

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=10s --retries=3 \
  CMD wget --quiet --tries=1 --spider http://localhost:8025 || exit 1

# Expose ports
EXPOSE 1025 8025

# Default command
CMD ["MailHog"]
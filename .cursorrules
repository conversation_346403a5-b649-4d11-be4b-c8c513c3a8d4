# gAds Supercharge - Cursor Rules

## Project Overview
This is a comprehensive Google Ads automation and management platform with full-stack architecture:
- **Frontend**: React 18 + TypeScript + Vite + Tailwind CSS
- **Backend**: Node.js + Express.js + PostgreSQL
- **Database**: PostgreSQL with multilingual content management
- **Deployment**: Docker + Docker Compose
- **Authentication**: JWT-based with session tracking
- **Internationalization**: Database-driven content (English/Ukrainian)

## Technology Stack Guidelines

### Frontend (React + TypeScript)
- Use React 18 with TypeScript for all components
- Vite 5.4.8 for build tooling
- Tailwind CSS with PostCSS for styling
- React Router DOM v7.6.0 for routing
- Radix UI with custom components for UI elements
- React Hook Form for form handling and validation
- Lucide React for icons

### Backend (Node.js + Express)
- Node.js 18+ with Express.js framework
- PostgreSQL 15 with connection pooling
- JWT tokens with session management
- bcryptjs for password hashing (12 rounds)
- Helmet, CORS, rate limiting for security
- Input validation with express-validator
- Comprehensive request and activity logging

### Database (PostgreSQL)
- Use parameterized queries to prevent SQL injection
- Follow the existing schema structure:
  - `users`: User authentication and preferences
  - `content_keys`: Multilingual content keys
  - `content_translations`: Language-specific translations (EN/UA)
  - `user_sessions`: Session tracking with browser/device info
  - `user_activities`: Comprehensive activity logging
  - `user_preferences`: User-specific settings
  - `admin_settings`: System configuration

## Coding Standards and Conventions

### File Organization
```
src/
├── components/
│   ├── ui/                    # Reusable UI components
│   ├── tools/                 # Tool-specific components (12 tools)
│   ├── dashboard/             # Dashboard components
│   └── [ComponentName].tsx    # Main components
├── pages/                     # Page components
├── contexts/                  # React contexts (Auth, Language, Sidebar)
├── hooks/                     # Custom React hooks
├── utils/                     # Utility functions
├── data/                      # Static data and configuration
├── types/                     # TypeScript type definitions
└── lib/                       # Library utilities
```

### Component Structure Rules
- Use functional components with TypeScript
- Follow the dark theme design system consistently:
  - Background: `bg-gray-900`
  - Text: `text-white`
  - Borders: `border-gray-700`
  - Cards: `bg-gray-800`
  - Accents: Blue (`blue-600`, `blue-700`)
- Use proper TypeScript interfaces for all props and data structures
- Implement proper error boundaries and loading states
- Use React Hook Form for all forms with validation

### API Development Guidelines
- All API routes must use authentication middleware (`authenticateToken`)
- Admin routes must use `requireAdmin` middleware
- Use proper HTTP status codes (200, 201, 400, 401, 403, 404, 500)
- Implement comprehensive error handling and logging
- Use input validation with express-validator
- Return consistent JSON response format:
  ```json
  {
    "data": {},
    "error": "Error message",
    "timestamp": "ISO string"
  }
  ```

### Database Interaction Patterns
- Always use parameterized queries: `pool.query('SELECT * FROM users WHERE id = $1', [userId])`
- Use connection pooling for database connections
- Implement proper transaction handling for multi-step operations
- Log all admin actions to `user_activities` table
- Use proper column naming: `activity_type` (not `action`), `activity_data` (not `details`)

### Security Guidelines
- **Input Validation**: Validate all user inputs with express-validator
- **Script Generation Security**: 
  - Use `escapeForScript()` function for all user inputs in generated scripts
  - Use `sanitizeJsonData()` for safe JSON.stringify operations
  - Implement unique variable suffixes to prevent conflicts
- **SQL Injection Prevention**: Always use parameterized queries
- **XSS Protection**: Sanitize content and use security headers
- **Password Security**: Use bcryptjs with 12 salt rounds
- **JWT Security**: 24-hour expiration with secure session management

### Authentication Patterns
- Email format: `[login]@gads-supercharge.today`
- JWT tokens stored in localStorage as `auth_token`
- Session tracking with browser and device information
- Role-based access control (admin, user roles)
- Protected routes using `ProtectedRoute` component

### Internationalization (i18n)
- Use database-driven translations via `useLanguage()` hook
- Access translations with `t('key.subkey')` function
- Keep technical terms in English (Google Ads, API, URL, ID, token)
- Translate user-facing content (buttons, messages, descriptions)
- Support English (en) and Ukrainian (ua) languages
- Implement fallback to English for missing translations

### Deployment and Docker Configuration
- Use multi-stage Docker builds for optimization
- Build for multi-platform support (linux/amd64, linux/arm64)
- Follow semantic versioning for Docker images
- Update `docker-compose.coolify.yml` after image rebuilds
- Use proper health checks for all services
- Environment-based configuration with `.env` files

### Error Handling
- Implement comprehensive error boundaries in React
- Use try-catch blocks for all async operations
- Log errors with context information
- Show user-friendly error messages
- Implement proper loading states and feedback

### Performance Guidelines
- Use React.lazy() for code splitting
- Implement proper memoization with useMemo/useCallback
- Optimize bundle size with tree shaking
- Use proper caching strategies for API calls
- Implement pagination for large data sets

### Testing Guidelines
- Write unit tests for utility functions
- Test API endpoints with proper authentication
- Test React components with React Testing Library
- Implement integration tests for critical user flows
- Test Docker builds and deployments

## Development Workflow
1. Always check existing patterns before implementing new features
2. Use TypeScript strictly - no `any` types
3. Follow the established dark theme design system
4. Test authentication and authorization thoroughly
5. Update documentation when adding new features
6. Use semantic commit messages
7. Test Docker builds before deployment

## Common Patterns
- Use `useAuth()` hook for authentication state
- Use `useLanguage()` hook for translations
- Use `useSidebar()` hook for sidebar state
- Implement proper loading states with `useState`
- Use consistent error handling patterns
- Follow the established API response format

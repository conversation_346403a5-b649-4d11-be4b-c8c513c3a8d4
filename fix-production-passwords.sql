-- Fix production passwords with correct hashes
-- Generated on 2025-07-15

-- Update admin password (Admin2025!Secure#)
UPDATE users SET password_hash = '$2a$12$gkeDk83gdN8CFmygvTPiNOzM0rZhI7rJyf9UnhzomgQd0e2ChcXaO' WHERE email = '<EMAIL>';

-- Update user password (User2025!Strong#)
UPDATE users SET password_hash = '$2a$12$stNVtPKZpExvTXO.LZDbLOaRvu5Vs9NsQA0es5xvbt95eedTz9N4G' WHERE email = '<EMAIL>';

-- Update demo password (Demo2025!Test#)
UPDATE users SET password_hash = '$2a$12$J3e40YtFdQrtzDRprQpaReBTGQvDSEIpzXHtz.dUCPdHogi3.1RUu' WHERE email = '<EMAIL>';

-- Update test password (Test2025!Complex#)
UPDATE users SET password_hash = '$2a$12$bFHso5s3mrr5qSrncNxlNeWFG8EZVSAzgur1ZMzTweOcGQ9ac58Bm' WHERE email = '<EMAIL>';

-- Verify updates
SELECT email, length(password_hash) as hash_length, 
       CASE WHEN password_hash = '$2a$12$hSTkxvDXTx.0TLBmuN7JMeYT3S6ZJVL1QFU6G7kTpbvVhAHshwH8G' 
            THEN 'CORRECT' 
            ELSE 'INCORRECT' 
       END as hash_status
FROM users 
WHERE email IN ('<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>')
ORDER BY email;

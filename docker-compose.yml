version: '3.8'

services:
  postgres:
    image: postgres:15-alpine
    environment:
      POSTGRES_DB: gads_db
      POSTGRES_USER: gads_user
      POSTGRES_PASSWORD: gads_password
      POSTGRES_HOST_AUTH_METHOD: trust
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./backend/database/init.sql:/docker-entrypoint-initdb.d/init.sql
    ports:
      - "5432:5432"
    restart: unless-stopped

  redis:
    image: redis:7-alpine
    command: redis-server --appendonly yes
    volumes:
      - redis_data:/data
    ports:
      - "6379:6379"
    restart: unless-stopped

  backend:
    build:
      context: .
      dockerfile: backend/Dockerfile
    environment:
      NODE_ENV: development
      PORT: 3001
      DB_HOST: postgres
      DB_PORT: 5432
      DB_NAME: gads_db
      DB_USER: gads_user
      DB_PASSWORD: gads_password
      JWT_SECRET: your-super-secret-jwt-key-change-in-production
      REDIS_HOST: redis
      REDIS_PORT: 6379
      CORS_ORIGIN: http://localhost:5173
      SMTP_HOST: mailhog
      SMTP_PORT: 1025
      EMAIL_FROM: noreply@localhost
      ADMIN_EMAIL: admin@localhost
    volumes:
      - ./backend:/app
      - /app/node_modules
    ports:
      - "3001:3001"
    depends_on:
      - postgres
      - redis
      - mailhog
    restart: unless-stopped

  frontend:
    build:
      context: .
      dockerfile: Dockerfile.frontend
    environment:
      VITE_API_URL: http://localhost:3001/api
      VITE_NODE_ENV: development
    volumes:
      - .:/app
      - /app/node_modules
      - /app/backend
    ports:
      - "5173:5173"
    depends_on:
      - backend
    restart: unless-stopped

  mailhog:
    image: mailhog/mailhog:latest
    environment:
      MH_STORAGE: maildir
      MH_MAILDIR_PATH: /tmp
    volumes:
      - mailhog_data:/tmp
    ports:
      - "1025:1025"
      - "8025:8025"
    restart: unless-stopped

volumes:
  postgres_data:
  redis_data:
  mailhog_data:

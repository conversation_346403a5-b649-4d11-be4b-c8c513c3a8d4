# ===============================================================================
# GADS SUPERCHARGE - NGINX MAILHOG PROXY
# ===============================================================================
# 🎯 NGINX REVERSE PROXY FOR MAILHOG WITH BASIC AUTH
# ✅ BASE: nginx:alpine
# ✅ AUTH: Basic Auth with htpasswd
# ✅ PROXY: Forwards to mailhog:8025
# ===============================================================================

FROM nginx:alpine

# Install apache2-utils for htpasswd
RUN apk add --no-cache apache2-utils

# Create htpasswd file with admin/mailhog123
RUN htpasswd -c -b /etc/nginx/.htpasswd admin mailhog123

# Copy nginx configuration
COPY nginx-mailhog.conf /etc/nginx/conf.d/default.conf

# Metadata
LABEL maintainer="<EMAIL>"
LABEL description="Nginx proxy for MailHog with Basic Auth"
LABEL version="v1.0.0"

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=10s --retries=3 \
  CMD wget --quiet --tries=1 --spider http://localhost:80 || exit 1

# Expose port
EXPOSE 80

# Start nginx
CMD ["nginx", "-g", "daemon off;"]